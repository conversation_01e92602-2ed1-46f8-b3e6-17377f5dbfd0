# .htaccess for pokemon-gamma-emerald.com

# 启用 URL 重写引擎
RewriteEngine On

# 将HTTP请求重定向到HTTPS
RewriteCond %{HTTPS} off
RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# 将 www 重定向到非 www 版本
RewriteCond %{HTTP_HOST} ^www\.pokemon-gamma-emerald\.com [NC]
RewriteRule ^(.*)$ https://pokemon-gamma-emerald.com/$1 [L,R=301]

# 确保sitemap.xml等特殊文件能够被直接访问
<FilesMatch "\.(xml|txt|json|webmanifest)$">
    <IfModule mod_headers.c>
        Header set Content-Type "text/plain"
    </IfModule>
</FilesMatch>

# 错误页面设置
ErrorDocument 404 /404.html
ErrorDocument 500 /500.html

# 内容安全策略设置
<IfModule mod_headers.c>
    Header set Content-Security-Policy "default-src 'self'; script-src 'self' https://cdnjs.cloudflare.com; style-src 'self' 'unsafe-inline' https://cdnjs.cloudflare.com; img-src 'self' https://images.pexels.com data:; font-src 'self' https://cdnjs.cloudflare.com; frame-src https://slitheriogame.io; connect-src 'self';"
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "SAMEORIGIN"
    Header set X-XSS-Protection "1; mode=block"
    Header set Referrer-Policy "strict-origin-when-cross-origin"
</IfModule>

# 设置文件缓存时间
<IfModule mod_expires.c>
    ExpiresActive On
    
    # 图片缓存 1 个月
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
    
    # CSS和JavaScript缓存 1 周
    ExpiresByType text/css "access plus 1 week"
    ExpiresByType application/javascript "access plus 1 week"
    
    # 其他资源缓存 2 天
    ExpiresDefault "access plus 2 days"
</IfModule>

# 启用GZIP压缩
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule> 