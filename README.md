🌍 *[English](README.md) ∙ [简体中文](README-zh.md)*


# Gaming Platform - Pokemon Game Collection

A modern, responsive gaming platform built with Next.js 15, featuring Pokemon games and other online games. This platform provides an excellent user experience for discovering and playing browser-based games.

## 🎮 Features

### Core Functionality
- **Game Discovery**: Browse games by categories (Pokemon, RPG, Adventure, etc.)
- **Dynamic Game Pages**: Individual pages for each game with embedded gameplay
- **Hot Games**: Curated collection of popular games
- **New Games**: Latest additions sorted by publication date
- **Related Games**: Smart recommendations based on game genres
- **Responsive Design**: Optimized for desktop, tablet, and mobile devices

### SEO & Performance
- **SEO Optimized**: Complete meta tags, Open Graph, Twitter Cards
- **Dynamic Sitemap**: Auto-generated sitemap including all game pages
- **Static Generation**: Pre-rendered pages for optimal performance
- **Image Optimization**: WebP/AVIF support with Next.js Image component
- **Error Handling**: Custom 404 page and error boundaries

### Technical Features
- **TypeScript**: Full type safety throughout the application
- **Markdown Content**: Game content managed via Markdown files
- **Dark Mode**: Theme switching support
- **Analytics**: Google Analytics integration
- **Security Headers**: Comprehensive security configuration

## 🛠 Tech Stack

- **Framework**: Next.js 15 (App Router)
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui
- **Content**: Markdown with gray-matter
- **Icons**: Lucide React, React Icons
- **Analytics**: Google Analytics
- **Deployment**: Vercel

## 📁 Project Structure

```
├── app/                          # Next.js App Router pages
│   ├── (home)/                   # Home page (core game)
│   ├── [game-slug]/              # Dynamic game pages
│   ├── new-games/                # Latest games listing
│   ├── hot-games/                # Popular games listing
│   ├── games/[genre]-games/      # Category pages
│   └── static pages/             # About, Contact, etc.
├── components/                   # React components
│   ├── ui/                       # Shadcn UI components
│   ├── GameCard.tsx              # Game preview cards
│   ├── GameDetail.tsx            # Game detail display
│   ├── Header.tsx                # Navigation header
│   ├── Sidebar.tsx               # Side navigation
│   └── Footer.tsx                # Site footer
├── lib/                          # Utility functions
│   ├── markdown.ts               # Markdown processing
│   └── types.ts                  # TypeScript definitions
├── games/                        # Game content (Markdown)
├── config/                       # Configuration files
│   └── hot-games.json            # Hot games configuration
└── public/images/thumbnails/     # Game thumbnails
```

## 🚀 Getting Started

### Prerequisites
- Node.js 18+ 
- npm or yarn

### Installation

1. Clone the repository:
```bash
git clone <repository-url>
cd pokemon-modele
```

2. Install dependencies:
```bash
npm install
```

3. Copy environment variables:
```bash
cp .env.example .env
```

4. Configure your environment variables in `.env`:
```env
# 生产环境使用实际域名，开发环境使用 localhost
NEXT_PUBLIC_BASE_URL=https://pokemon-gamma-emerald.com
NEXT_PUBLIC_GA_TRACKING_ID=your-ga-tracking-id
```

5. Start the development server:
```bash
npm run dev
```

Visit `http://localhost:3000` to see the application in development mode.

## 📝 Content Management

### Adding New Games

1. Create a new Markdown file in the `games/` directory:
```markdown
---
title: "Game Title"
slug: "game-slug"
thumbnail: "/images/thumbnails/game-thumbnail.png"
genres: ["Pokemon games", "RPG"]
description: "Game description for SEO and cards"
iframeUrl: "https://game-url.com"
publishedDate: "2024-01-01"
author: "Author Name"
---

# Game Title

Game content in Markdown format...
```

2. Add the game thumbnail to `public/images/thumbnails/`

3. For hot games, add the slug to `config/hot-games.json`:
```json
[
  { "slug": "game-slug" }
]
```

### Supported Game Categories
- Pokemon games
- RPG games  
- Adventure games
- Action games
- Puzzle games
- Strategy games
- Simulation games

## 🔧 Configuration

### Site Configuration
Edit `config/site.ts` to customize:
- Site name and description
- SEO metadata
- Social media links
- Analytics settings

### Navigation
- Header navigation: `components/header/Header.tsx`
- Sidebar navigation: `components/Sidebar.tsx`
- Footer links: `components/footer/Footer.tsx`

## 📊 SEO Features

- **Dynamic Meta Tags**: Each page has optimized title, description, and keywords
- **Open Graph**: Rich social media previews
- **Twitter Cards**: Enhanced Twitter sharing
- **Structured Data**: JSON-LD for games and organization
- **Sitemap**: Auto-generated XML sitemap
- **Robots.txt**: Search engine crawling instructions

## 🚀 Deployment

### Vercel (Recommended)
1. Push your code to GitHub
2. Connect your repository to Vercel
3. Configure environment variables
4. Deploy automatically

### Manual Build
```bash
npm run build
npm start
```

## 🧪 Testing

```bash
# Type checking
npx tsc --noEmit

# Linting
npm run lint

# Build test
npm run build
```

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

Contributions are welcome! Please feel free to submit a Pull Request.

## 📞 Support

If you have any questions or need help, please:
- Open an issue on GitHub
- Contact us through the website's contact page

---

Built with ❤️ using Next.js 15 and modern web technologies.

