import { SiteConfig } from "@/types/siteConfig";

export const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || "https://pokemon-gamma-emerald.com";

const baseSiteConfig = {
  name: "pokemon-gamma-emerald.com",
  description:
    "Your ultimate destination for Pokemon gaming experiences. Discover, play, and enjoy amazing Pokemon games online.",
  url: BASE_URL,
  metadataBase: new URL(BASE_URL),
  authors: [
    {
      name: "pokemon-gamma-emerald.com",
      url: "https://pokemon-gamma-emerald.com",
    }
  ],
  creator: '@pokemon-gamma-emerald',
  themeColors: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: 'black' },
  ],
  defaultNextTheme: 'system', // next-theme option: system | dark | light
  icons: {
    icon: "/favicon.ico",
    shortcut: "/favicon-32x32.png",
    apple: "/android-chrome-192x192.png", // apple-touch-icon.png
  },
}

export const siteConfig: SiteConfig = {
  ...baseSiteConfig,
  openGraph: {
    type: "website",
    locale: "en-US",
    url: baseSiteConfig.url,
    title: baseSiteConfig.name,
    description: baseSiteConfig.description,
    siteName: baseSiteConfig.name,
    images: [`${baseSiteConfig.url}/og.png`],
  },
  twitter: {
    card: "summary_large_image",
    title: baseSiteConfig.name,
    site: baseSiteConfig.url,
    description: baseSiteConfig.description,
    images: [`${baseSiteConfig.url}/og.png`],
    creator: baseSiteConfig.creator,
  },
}
