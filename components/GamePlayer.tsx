"use client";

import React, { useState, useRef, useEffect } from 'react';
import { Heart, Maximize, Play, Pause, Volume2, VolumeX, PlayCircle } from 'lucide-react';
import { useFavorites } from '@/hooks/useFavorites';
import { GameFrontmatter } from '@/lib/types';
import ShareDropdown from './ShareDropdown';
import BookmarkButton from './BookmarkButton';
import Image from 'next/image';
import { generateGameCoverAlt } from '@/lib/alt-text-utils';

interface GamePlayerProps {
  /** Game title, displayed in the bottom extension bar */
  title: string;
  /** Display title from H1 in markdown content */
  displayTitle?: string | null;
  /** Game thumbnail for play button background */
  thumbnail?: string;
  /** iframe embed URL */
  iframeUrl: string;
  /** Game slug for favorites and sharing functionality */
  gameSlug?: string;
  /** Game data for favorites functionality */
  gameData?: GameFrontmatter;
  /** Custom CSS class name */
  className?: string;
  /** Whether to show play control buttons */
  showPlayControls?: boolean;
  /** Favorite status callback */
  onFavorite?: (isFavorited: boolean) => void;
  /** Share callback */
  onShare?: () => void;
  /** Fullscreen callback */
  onFullscreen?: () => void;
}

export default function GamePlayer({
  title,
  displayTitle,
  thumbnail,
  iframeUrl,
  gameSlug,
  gameData,
  className = '',
  showPlayControls = false,
  onFavorite,
  onShare,
  onFullscreen
}: GamePlayerProps) {
  const [isPlaying, setIsPlaying] = useState(true);
  const [isMuted, setIsMuted] = useState(false);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showIframe, setShowIframe] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const playerRef = useRef<HTMLDivElement>(null);
  const iframeRef = useRef<HTMLIFrameElement>(null);

  // 使用收藏功能Hook
  const { isFavorited, toggleFavorite } = useFavorites();

  // 检查当前游戏是否已收藏
  const currentGameFavorited = gameSlug ? isFavorited(gameSlug) : false;

  // 获取显示标题，优先使用displayTitle，回退到title
  const getDisplayTitle = () => {
    return displayTitle || title;
  };

  // Handle play button click
  const handlePlayClick = () => {
    setIsLoading(true);
    setShowIframe(true);
    // 模拟加载时间
    setTimeout(() => {
      setIsLoading(false);
    }, 1000);
  };

  // Handle favorite functionality
  const handleFavorite = () => {
    if (!gameSlug || !gameData) {
      console.warn('Game slug or data not provided for favorites functionality');
      return;
    }

    const newFavoriteState = toggleFavorite(gameData);
    onFavorite?.(newFavoriteState);
  };

  // Handle fullscreen functionality
  const handleFullscreen = () => {
    if (!playerRef.current) return;

    if (!isFullscreen) {
      // Enter fullscreen
      if (playerRef.current.requestFullscreen) {
        playerRef.current.requestFullscreen();
      } else if ((playerRef.current as any).webkitRequestFullscreen) {
        (playerRef.current as any).webkitRequestFullscreen();
      } else if ((playerRef.current as any).msRequestFullscreen) {
        (playerRef.current as any).msRequestFullscreen();
      }
    } else {
      // Exit fullscreen
      if (document.exitFullscreen) {
        document.exitFullscreen();
      } else if ((document as any).webkitExitFullscreen) {
        (document as any).webkitExitFullscreen();
      } else if ((document as any).msExitFullscreen) {
        (document as any).msExitFullscreen();
      }
    }
    setIsFullscreen(!isFullscreen);
    onFullscreen?.();
  };

  // Listen for fullscreen state changes
  const handleFullscreenChange = () => {
    const isCurrentlyFullscreen = !!(
      document.fullscreenElement ||
      (document as any).webkitFullscreenElement ||
      (document as any).msFullscreenElement
    );
    setIsFullscreen(isCurrentlyFullscreen);
  };

  // Add fullscreen event listeners
  useEffect(() => {
    document.addEventListener('fullscreenchange', handleFullscreenChange);
    document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
    document.addEventListener('msfullscreenchange', handleFullscreenChange);

    return () => {
      document.removeEventListener('fullscreenchange', handleFullscreenChange);
      document.removeEventListener('webkitfullscreenchange', handleFullscreenChange);
      document.removeEventListener('msfullscreenchange', handleFullscreenChange);
    };
  }, []);

  if (!iframeUrl) {
    return (
      <div className={`bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden ${className}`}>
        {/* Game area placeholder */}
        <div className="aspect-video flex items-center justify-center">
          <div className="text-center">
            <div className="text-4xl mb-4">🎮</div>
            <p className="text-gray-600 dark:text-gray-300">
              Game content not available
            </p>
          </div>
        </div>
        
        {/* Bottom extension bar */}
        <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            <h1 className="text-lg font-semibold text-gray-900 dark:text-white truncate flex-1 mr-4">
              {getDisplayTitle()}
            </h1>
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-500 dark:text-gray-400">Game not available</span>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div 
      ref={playerRef}
      className={`bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden ${className} ${
        isFullscreen ? 'fixed inset-0 z-50 rounded-none' : ''
      }`}
    >
      {/* iframe game area */}
      <div className={`${isFullscreen ? 'h-full' : 'aspect-video'} relative`}>
        {!showIframe ? (
          /* Play Now Overlay */
          <div className="w-full h-full relative bg-gray-900 flex items-center justify-center">
            {/* Background Image with Blur */}
            {thumbnail && (
              <div className="absolute inset-0">
                <Image
                  src={thumbnail}
                  alt={generateGameCoverAlt(getDisplayTitle(), gameSlug)}
                  fill
                  className="object-cover filter blur-sm opacity-60"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 70vw"
                  priority
                />
                <div className="absolute inset-0 bg-black bg-opacity-40"></div>
              </div>
            )}
            
            {/* Play Button */}
            <button
              onClick={handlePlayClick}
              disabled={isLoading}
              className="relative z-10 group flex flex-col items-center justify-center p-8 rounded-2xl bg-black bg-opacity-50 hover:bg-opacity-70 transition-all duration-300 transform hover:scale-105"
            >
              {isLoading ? (
                <div className="w-16 h-16 border-4 border-white border-t-transparent rounded-full animate-spin mb-4"></div>
              ) : (
                <PlayCircle className="w-16 h-16 text-white mb-4 group-hover:text-blue-400 transition-colors" />
              )}
              <span className="text-white text-xl font-semibold group-hover:text-blue-400 transition-colors">
                {isLoading ? 'Loading...' : 'Play Now'}
              </span>
              <span className="text-gray-300 text-sm mt-2">
                Click to start the game
              </span>
            </button>
          </div>
        ) : (
          /* Actual iframe */
        <iframe
          ref={iframeRef}
          src={iframeUrl}
            title={`Play ${getDisplayTitle()}`}
          className="w-full h-full border-0"
          allowFullScreen
          loading="lazy"
          allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
          onError={(e) => {
            console.error('Failed to load game iframe:', e);
          }}
        />
        )}
      </div>

      {/* Bottom extension bar - only show when not in fullscreen */}
      {!isFullscreen && (
        <div className="bg-white dark:bg-gray-800 border-t border-gray-200 dark:border-gray-700 p-4">
          <div className="flex items-center justify-between">
            {/* Left side: Game title (H1) */}
            <div className="flex-1 mr-4">
              <h1 className="text-lg font-semibold text-gray-900 dark:text-white truncate">
                {getDisplayTitle()}
              </h1>
            </div>

            {/* Right side: Function buttons */}
            <div className="flex items-center gap-3">
              {/* Play control buttons (optional) */}
              {showPlayControls && showIframe && (
                <>
                  <button
                    onClick={() => setIsPlaying(!isPlaying)}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors text-gray-600 dark:text-gray-400"
                    title={isPlaying ? 'Pause' : 'Play'}
                  >
                    {isPlaying ? (
                      <Pause className="w-5 h-5" />
                    ) : (
                      <Play className="w-5 h-5" />
                    )}
                  </button>

                  <button
                    onClick={() => setIsMuted(!isMuted)}
                    className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors text-gray-600 dark:text-gray-400"
                    title={isMuted ? 'Unmute' : 'Mute'}
                  >
                    {isMuted ? (
                      <VolumeX className="w-5 h-5" />
                    ) : (
                      <Volume2 className="w-5 h-5" />
                    )}
                  </button>

                  <div className="w-px h-6 bg-gray-300 dark:bg-gray-600 mx-1" />
                </>
              )}

              {/* Favorite button */}
              <button
                onClick={handleFavorite}
                className={`p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors ${
                  currentGameFavorited ? 'text-red-500' : 'text-gray-600 dark:text-gray-400'
                }`}
                title={currentGameFavorited ? 'Remove from favorites' : 'Add to favorites'}
                disabled={!gameSlug || !gameData}
              >
                <Heart className={`w-5 h-5 ${currentGameFavorited ? 'fill-current' : ''}`} />
              </button>

              {/* Bookmark button */}
              {gameSlug && (
                <BookmarkButton
                  title={getDisplayTitle()}
                  gameSlug={gameSlug}
                />
              )}

              {/* Share dropdown */}
              {gameSlug && (
                <ShareDropdown
                  title={getDisplayTitle()}
                  gameSlug={gameSlug}
                />
              )}

              {/* Fullscreen button */}
              <button
                onClick={handleFullscreen}
                className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors text-gray-600 dark:text-gray-400"
                title={isFullscreen ? 'Exit fullscreen' : 'Enter fullscreen'}
              >
                <Maximize className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 