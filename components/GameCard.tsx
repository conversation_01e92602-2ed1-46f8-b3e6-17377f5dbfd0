"use client";

import React, { useState } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import { GameFrontmatter } from '@/lib/types';
import { generateGameCoverAlt } from '@/lib/alt-text-utils';

// 热度标签类型定义
type HotTag = 'hot' | 'new' | 'top_rated';

// 尺寸变体定义
type CardSize = 'small' | 'medium' | 'large';

interface GameCardProps {
  /** 游戏数据 */
  game: GameFrontmatter;
  /** 热度标签 */
  tags?: HotTag[];
  /** 卡片尺寸 */
  size?: CardSize;
  /** 自定义点击处理 */
  onClick?: (gameId: string) => void;
  /** 自定义样式类名 */
  className?: string;
  /** 是否启用懒加载 */
  lazy?: boolean;
}

// 骨架屏组件
function GameCardSkeleton({ size = 'medium' }: { size?: CardSize }) {
  const sizeClasses = {
    small: 'w-full max-w-[200px] aspect-square',
    medium: 'w-full max-w-[280px] aspect-square',
    large: 'w-full max-w-[320px] aspect-square'
  };

  return (
    <div className={`${sizeClasses[size]} mx-auto`}>
      <div className="w-full h-full bg-gray-200 dark:bg-gray-700 rounded-lg overflow-hidden shadow-sm animate-pulse">
        <div className="w-full h-full bg-gray-300 dark:bg-gray-600" />
      </div>
    </div>
  );
}

// 热度标签组件
function HotTagBadge({ tag }: { tag: HotTag }) {
  const tagConfig = {
    hot: {
      label: 'HOT',
      className: 'bg-red-500 text-white',
      icon: '🔥'
    },
    new: {
      label: 'NEW',
      className: 'bg-green-500 text-white',
      icon: '✨'
    },
    top_rated: {
      label: 'TOP RATED',
      className: 'bg-yellow-500 text-black',
      icon: '⭐'
    }
  };

  const config = tagConfig[tag];

  return (
    <div className={`
      absolute top-2 left-2 z-10 px-2 py-1 rounded text-xs font-bold
      shadow-md backdrop-blur-sm ${config.className}
      flex items-center gap-1
    `}>
      <span className="text-xs">{config.icon}</span>
      <span>{config.label}</span>
    </div>
  );
}

export default function GameCard({
  game,
  tags = [],
  size = 'medium',
  onClick,
  className = '',
  lazy = true
}: GameCardProps) {
  const [imageLoading, setImageLoading] = useState(true);
  const [imageError, setImageError] = useState(false);

  // 空值检查
  if (!game) {
    return <GameCardSkeleton size={size} />;
  }

  const {
    title = 'Untitled Game',
    displayTitle,
    slug = '',
    thumbnail = '/images/thumbnails/default.svg',
    genres = [],
    description = 'No description available'
  } = game;

  // 获取显示标题，优先使用displayTitle，回退到title
  const getDisplayTitle = () => {
    return displayTitle || title;
  };

  // 无效数据检查
  if (!slug) {
    return <GameCardSkeleton size={size} />;
  }

  // 尺寸样式配置 - 改为正方形
  const sizeClasses = {
    small: {
      container: 'w-full max-w-[200px] aspect-square',
      title: 'text-sm'
    },
    medium: {
      container: 'w-full max-w-[280px] aspect-square',
      title: 'text-base'
    },
    large: {
      container: 'w-full max-w-[320px] aspect-square',
      title: 'text-lg'
    }
  };

  const currentSize = sizeClasses[size];

  // 处理点击事件
  const handleClick = (e: React.MouseEvent) => {
    if (onClick) {
      e.preventDefault();
      onClick(slug);
    }
  };

  // 处理图片加载
  const handleImageLoad = () => {
    setImageLoading(false);
  };

  const handleImageError = () => {
    setImageLoading(false);
    setImageError(true);
  };

  return (
    <div className={`${currentSize.container} mx-auto ${className}`}>
      <Link 
        href={`/${slug}`}
        onClick={handleClick}
        className="group block focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg"
        aria-label={`Play ${title} - ${description}`}
      >
        <article className="
          relative w-full h-full bg-white dark:bg-gray-800 rounded-lg overflow-hidden
          shadow-[0_2px_8px_rgba(0,0,0,0.1)] dark:shadow-[0_2px_8px_rgba(0,0,0,0.3)]
          border border-gray-200 dark:border-gray-700
          transition-all duration-300 ease-out
          hover:shadow-[0_8px_24px_rgba(0,0,0,0.15)] dark:hover:shadow-[0_8px_24px_rgba(0,0,0,0.4)]
          hover:-translate-y-1
          active:scale-[0.98] active:translate-y-0
          cursor-pointer
          min-h-[200px]
        ">
          {/* 热度标签 */}
          {tags.length > 0 && (
            <HotTagBadge tag={tags[0]} />
          )}

          {/* 图片加载状态 */}
          {imageLoading && (
            <div className="absolute inset-0 bg-gray-200 dark:bg-gray-600 animate-pulse flex items-center justify-center">
              <div className="text-gray-400 dark:text-gray-500">
                <svg className="w-8 h-8 animate-spin" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
                </svg>
              </div>
            </div>
          )}

          {/* 主图片 - 填满整个卡片 */}
          <Image
            src={imageError ? '/images/thumbnails/default.svg' : thumbnail}
            alt={generateGameCoverAlt(title, slug)}
            fill
            className={`
              object-cover transition-transform duration-300
              group-hover:scale-110
              ${imageLoading ? 'opacity-0' : 'opacity-100'}
            `}
            sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw"
            loading={lazy ? 'lazy' : 'eager'}
            onLoad={handleImageLoad}
            onError={handleImageError}
            priority={!lazy}
          />

          {/* 悬停时显示的游戏标题 - 底部渐变背景 */}
          <div className="
            absolute bottom-0 left-0 right-0
            bg-gradient-to-t from-black/80 via-black/40 to-transparent
            opacity-0 group-hover:opacity-100
            transition-opacity duration-300
            p-4
          ">
            <div className={`
              font-semibold text-white 
              ${currentSize.title} leading-tight
              line-clamp-2
              drop-shadow-lg
            `}>
              {getDisplayTitle()}
            </div>
          </div>

          {/* 悬停播放图标 */}
          <div className="
            absolute inset-0 bg-black/0 group-hover:bg-black/20 
            transition-colors duration-300
            flex items-center justify-center
          ">
            <div className="
              opacity-0 group-hover:opacity-100 
              transition-opacity duration-300
              bg-white/90 dark:bg-gray-900/90 
              rounded-full p-3 backdrop-blur-sm
            ">
              <svg className="w-6 h-6 text-gray-900 dark:text-white" fill="currentColor" viewBox="0 0 24 24">
                <path d="M8 5v14l11-7z" />
              </svg>
            </div>
          </div>
        </article>
      </Link>
    </div>
  );
}

// 导出骨架屏组件供外部使用
export { GameCardSkeleton }; 