"use client";

import React, { memo, useMemo } from 'react';
import Link from 'next/link';
import GameCard, { GameCardSkeleton } from './GameCard';
import { GameFrontmatter } from '@/lib/types';
import { RefreshCw, ChevronRight, AlertCircle, Gamepad2 } from 'lucide-react';

// 热度标签类型定义（与GameCard保持一致）
type HotTag = 'hot' | 'new' | 'top_rated';

// 游戏数据接口（适配现有的GameFrontmatter）
interface GameData extends GameFrontmatter {
  gameId?: string;
  coverImage?: string;
}

// 列数配置接口
interface ColumnsConfig {
  desktop: number;
  tablet: number;
  mobile: number;
}

// 主组件Props接口
interface GameRecommendSectionProps {
  /** 推荐区域标题 */
  title: string;
  /** 游戏数据列表 */
  games: GameData[];
  /** 加载状态 */
  loading?: boolean;
  /** 错误信息 */
  error?: string;
  /** 布局模式 */
  layout?: 'grid' | 'scroll';
  /** 响应式列数配置 */
  columns?: ColumnsConfig;
  /** 是否显示"查看更多"按钮 */
  showMoreButton?: boolean;
  /** 查看更多回调 */
  onShowMore?: () => void;
  /** 刷新回调 */
  onRefresh?: () => void;
  /** 自定义样式类名 */
  className?: string;
  /** 卡片尺寸 */
  cardSize?: 'small' | 'medium' | 'large';
  /** 是否显示热度标签 */
  showTags?: boolean;
  /** 最大显示数量 */
  maxItems?: number;
  /** 容器背景样式 */
  background?: 'light' | 'transparent' | 'dark';
  /** 标题颜色 */
  titleColor?: string;
  /** 点击跳转链接 */
  linkTo?: string;
}

// 空数据状态组件
const EmptyState = memo(({ onRefresh }: { onRefresh?: () => void }) => (
  <div className="text-center py-8">
    <div className="text-4xl mb-4">🎮</div>
    <p className="text-gray-600 dark:text-gray-300 text-sm">
      No games available at the moment.
    </p>
    {onRefresh && (
      <button
        onClick={onRefresh}
        className="inline-flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors mt-4"
      >
        <RefreshCw className="w-4 h-4" />
        Refresh
      </button>
    )}
  </div>
));

EmptyState.displayName = 'EmptyState';

// 错误状态组件
const ErrorState = memo(({ error, onRefresh }: { error: string; onRefresh?: () => void }) => (
  <div className="text-center py-8">
    <div className="text-4xl mb-4">⚠️</div>
    <p className="text-gray-600 dark:text-gray-300">
      {error || 'An error occurred while loading games.'}
    </p>
    {onRefresh && (
      <button
        onClick={onRefresh}
        className="inline-flex items-center gap-2 px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors mt-4"
      >
        <RefreshCw className="w-4 h-4" />
        Retry
      </button>
    )}
  </div>
));

ErrorState.displayName = 'ErrorState';

// 加载状态组件
const LoadingState = memo(({ 
  columns, 
  cardSize = 'medium',
  layout = 'grid' 
}: { 
  columns: ColumnsConfig; 
  cardSize?: 'small' | 'medium' | 'large';
  layout?: 'grid' | 'scroll';
}) => {
  const skeletonCount = 6; // 默认显示6个骨架屏

  if (layout === 'scroll') {
    return (
      <div className="overflow-x-auto pb-4">
        <div className="flex gap-4 w-max">
          {Array.from({ length: skeletonCount }).map((_, index) => (
            <div key={index} className="flex-shrink-0">
              <GameCardSkeleton size={cardSize} />
            </div>
          ))}
        </div>
      </div>
    );
  }

  // 网格布局的响应式类名
  const gridClasses = `
    grid gap-4 sm:gap-6
    grid-cols-${columns.mobile}
    sm:grid-cols-${columns.tablet}
    lg:grid-cols-${columns.desktop}
  `;

  return (
    <div className={gridClasses}>
      {Array.from({ length: skeletonCount }).map((_, index) => (
        <GameCardSkeleton key={index} size={cardSize} />
      ))}
    </div>
  );
});

LoadingState.displayName = 'LoadingState';

// 主组件
const GameRecommendSection = memo<GameRecommendSectionProps>(({
  title,
  games,
  loading = false,
  error,
  layout = 'grid',
  columns = { desktop: 4, tablet: 2, mobile: 1 },
  showMoreButton = false,
  onShowMore,
  onRefresh,
  className = '',
  cardSize = 'medium',
  showTags = true,
  maxItems,
  background = 'transparent',
  titleColor,
  linkTo
}) => {
  // 处理显示的游戏数据
  const displayGames = useMemo(() => {
    if (!games || games.length === 0) return [];
    return maxItems ? games.slice(0, maxItems) : games;
  }, [games, maxItems]);

  // 生成热度标签
  const getGameTags = (game: GameData, index: number): HotTag[] => {
    if (!showTags) return [];
    
    // 简单的标签生成逻辑，可以根据实际需求调整
    if (index === 0) return ['hot'];
    if (game.publishedDate && new Date(game.publishedDate) > new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)) {
      return ['new'];
    }
    return [];
  };

  // 背景样式
  const backgroundClasses = {
    light: 'bg-gray-50 dark:bg-gray-800/50',
    transparent: 'bg-transparent',
    dark: 'bg-gray-900 dark:bg-gray-800'
  };

  // 网格布局的响应式类名
  const gridClasses = useMemo(() => {
    if (layout === 'scroll') return '';
    
    return `
      grid gap-4 sm:gap-6
      grid-cols-${columns.mobile}
      sm:grid-cols-${columns.tablet}
      lg:grid-cols-${columns.desktop}
    `.trim();
  }, [layout, columns]);

  // 容器内容
  const containerContent = (
    <section 
      className={`
        rounded-xl p-4
        ${backgroundClasses[background]}
        ${background !== 'transparent' ? 'shadow-sm border border-gray-200 dark:border-gray-700' : ''}
        ${className}
      `}
      role="region"
      aria-label={`${title} games section`}
      data-noindex="true"
      aria-hidden="true"
    >
      {/* 内容区域 - 移除标题，只显示游戏卡片 */}
      <div className="min-h-[200px]">
        {/* 加载状态 */}
        {loading && (
          <LoadingState 
            columns={columns} 
            cardSize={cardSize}
            layout={layout}
          />
        )}

        {/* 错误状态 */}
        {!loading && error && (
          <ErrorState error={error} onRefresh={onRefresh} />
        )}

        {/* 空数据状态 */}
        {!loading && !error && displayGames.length === 0 && (
          <EmptyState onRefresh={onRefresh} />
        )}

        {/* 正常内容 */}
        {!loading && !error && displayGames.length > 0 && (
          <>
            {layout === 'scroll' ? (
              // 横向滚动布局
              <div className="overflow-x-auto pb-4">
                <div className="flex gap-4 w-max">
                  {displayGames.map((game, index) => (
                    <div key={game.slug || game.gameId || index} className="flex-shrink-0">
                      <GameCard
                        game={game}
                        size={cardSize}
                        tags={getGameTags(game, index)}
                        className={cardSize === 'medium' ? 'w-[280px]' : cardSize === 'large' ? 'w-[320px]' : 'w-[200px]'}
                        lazy={index > 3} // 前4个不懒加载
                      />
                    </div>
                  ))}
                </div>
              </div>
            ) : (
              // 网格布局
              <div className={gridClasses}>
                {displayGames.map((game, index) => (
                  <GameCard
                    key={game.slug || game.gameId || index}
                    game={game}
                    size={cardSize}
                    tags={getGameTags(game, index)}
                    lazy={index > 5} // 前6个不懒加载
                  />
                ))}
              </div>
            )}
          </>
        )}
      </div>
    </section>
  );

  // 直接返回容器内容，不再用Link包装
  return containerContent;
});

GameRecommendSection.displayName = 'GameRecommendSection';

export default GameRecommendSection;
export type { GameRecommendSectionProps, GameData, ColumnsConfig }; 