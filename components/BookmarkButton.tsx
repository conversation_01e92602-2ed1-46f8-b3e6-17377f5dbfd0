"use client";

import React, { useState, useEffect } from 'react';
import { Bookmark, BookmarkCheck } from 'lucide-react';

interface BookmarkButtonProps {
  /** 游戏标题 */
  title: string;
  /** 游戏slug，用于构建书签链接 */
  gameSlug: string;
  /** 自定义CSS类名 */
  className?: string;
}

export default function BookmarkButton({ title, gameSlug, className = '' }: BookmarkButtonProps) {
  const [isBookmarked, setIsBookmarked] = useState(false);
  const [showTooltip, setShowTooltip] = useState(false);
  const [isMounted, setIsMounted] = useState(false);

  // 确保组件已挂载到客户端
  useEffect(() => {
    setIsMounted(true);
  }, []);

  // 添加书签功能
  const addBookmark = async () => {
    if (!isMounted) return;
    
    const url = `${window.location.origin}/${gameSlug}`;
    
    try {
      // 检查浏览器是否支持书签API
      if ('addToHomescreen' in window || 'external' in window) {
        // 对于支持的浏览器，尝试添加书签
        if ((window as any).external && (window as any).external.AddFavorite) {
          // Internet Explorer
          (window as any).external.AddFavorite(url, title);
          setIsBookmarked(true);
          setShowTooltip(true);
          setTimeout(() => setShowTooltip(false), 2000);
        } else if ((window as any).sidebar && (window as any).sidebar.addPanel) {
          // Firefox (旧版本)
          (window as any).sidebar.addPanel(title, url, '');
          setIsBookmarked(true);
          setShowTooltip(true);
          setTimeout(() => setShowTooltip(false), 2000);
        } else {
          // 对于现代浏览器，显示提示信息
          showBookmarkInstructions();
        }
      } else {
        // 显示手动添加书签的说明
        showBookmarkInstructions();
      }
    } catch (error) {
      console.error('Error adding bookmark:', error);
      showBookmarkInstructions();
    }
  };

  // 显示书签添加说明
  const showBookmarkInstructions = () => {
    if (!isMounted) return;
    
    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0;
    const shortcut = isMac ? 'Cmd+D' : 'Ctrl+D';
    
    alert(`To bookmark this page:\n\n1. Press ${shortcut}\n2. Or use your browser's bookmark menu\n3. Choose "Add to Bookmarks" or "Add to Favorites"`);
    
    setIsBookmarked(true);
    setShowTooltip(true);
    setTimeout(() => setShowTooltip(false), 3000);
  };

  // 如果还没有挂载到客户端，显示简单的书签按钮
  if (!isMounted) {
    return (
      <div className={`relative ${className}`}>
        <button
          className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors text-gray-600 dark:text-gray-400"
          title="Add to bookmarks"
          aria-label="Add to bookmarks"
          disabled
        >
          <Bookmark className="w-5 h-5" />
        </button>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={addBookmark}
        className={`p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors ${
          isBookmarked 
            ? 'text-yellow-500' 
            : 'text-gray-600 dark:text-gray-400'
        }`}
        title="Add to bookmarks"
        aria-label="Add to bookmarks"
        onMouseEnter={() => setShowTooltip(true)}
        onMouseLeave={() => setShowTooltip(false)}
      >
        {isBookmarked ? (
          <BookmarkCheck className="w-5 h-5" />
        ) : (
          <Bookmark className="w-5 h-5" />
        )}
      </button>

      {/* 提示工具栏 */}
      {showTooltip && (
        <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-1 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-md whitespace-nowrap z-50">
          {isBookmarked ? 'Bookmark added!' : 'Add to bookmarks'}
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-0 h-0 border-l-4 border-r-4 border-t-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
        </div>
      )}
    </div>
  );
} 