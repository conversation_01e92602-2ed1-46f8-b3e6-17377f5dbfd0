"use client";

import Image from 'next/image';
import { Game } from '@/lib/types';
import GamePlayer from './GamePlayer';
import { generateGamePreviewAlt } from '@/lib/alt-text-utils';

interface GameDetailProps {
  game: Game;
}

export default function GameDetail({ game }: GameDetailProps) {
  // Add null checks for game data
  if (!game) {
    return (
      <div className="max-w-4xl mx-auto p-6">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center">
          <h2 className="text-xl font-semibold text-red-800 dark:text-red-200 mb-2">
            Game Not Found
          </h2>
          <p className="text-red-600 dark:text-red-300">
            The requested game could not be loaded. Please try again later.
          </p>
        </div>
      </div>
    );
  }

  const {
    title = 'Untitled Game',
    slug = '',
    thumbnail = '/images/thumbnails/default.svg',
    genres = [],
    description = 'No description available',
    pageDescription,
    iframeUrl = '',
    author = '',
    contentHtml = ''
  } = game;

  // 处理收藏功能
  const handleFavorite = (isFavorited: boolean) => {
    // 这里可以添加收藏逻辑，比如保存到localStorage或发送到API
    console.log(`Game ${title} ${isFavorited ? 'added to' : 'removed from'} favorites`);
  };

  // 处理分享功能
  const handleShare = () => {
    console.log(`Sharing game: ${title}`);
  };

  // 处理全屏功能
  const handleFullscreen = () => {
    console.log(`Fullscreen toggled for game: ${title}`);
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      {/* 面包屑导航 - 这部分应该在调用此组件的页面中处理 */}
      
      {/* Game Player - H1标题现在显示在iframe底部栏中 */}
      <div className="mb-8">
        <GamePlayer
          title={title}
          iframeUrl={iframeUrl}
          gameSlug={slug}
          gameData={{
            title,
            slug,
            thumbnail,
            genres,
            description,
            iframeUrl,
            publishedDate: game.publishedDate,
            author,
            version: game.version,
            pageDescription
          }}
          className="w-full max-w-4xl mx-auto"
          showPlayControls={false}
          onFavorite={handleFavorite}
          onShare={handleShare}
          onFullscreen={handleFullscreen}
        />
      </div>

      {/* Game Meta Information - 移除发布日期显示 */}
      <div className="mb-6">
        {/* Genre Tags */}
        {genres.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {genres.map((genre) => (
              <span
                key={genre}
                className="inline-block bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 text-sm px-3 py-1 rounded-full font-medium"
              >
                {genre}
              </span>
            ))}
          </div>
        )}
        
        {/* Game Description - 优先使用pageDescription，回退到description */}
        <p className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed">
          {pageDescription || description}
        </p>
        
        {/* Author - 如果有的话 */}
        {author && (
          <p className="text-gray-600 dark:text-gray-400 text-sm mt-2">
            Created by {author}
          </p>
        )}
      </div>

      {/* Game Thumbnail - 移到侧边或作为预览图 */}
      {thumbnail && (
        <div className="mb-8">
          <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-4">
            Game Preview
          </h3>
          <div className="relative w-64 h-64 bg-gray-100 dark:bg-gray-800 rounded-lg overflow-hidden mx-auto lg:mx-0">
            <Image
              src={thumbnail}
              alt={generateGamePreviewAlt(title, slug)}
              fill
              className="object-cover"
              sizes="256px"
              onError={(e) => {
                // Fallback to default image on error
                const target = e.target as HTMLImageElement;
                target.src = '/images/thumbnails/default.svg';
              }}
            />
          </div>
        </div>
      )}

      {/* Game Description Content - 现在从H2开始，因为H1已被移除 */}
      {contentHtml && (
        <div className="prose prose-lg dark:prose-invert max-w-none">
          <div 
            dangerouslySetInnerHTML={{ __html: contentHtml }}
            className="text-gray-700 dark:text-gray-300"
          />
        </div>
      )}
    </div>
  );
} 