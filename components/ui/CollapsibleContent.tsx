"use client";

import React, { useState, useRef, useEffect } from 'react';
import { ChevronDown, ChevronUp } from 'lucide-react';

interface CollapsibleContentProps {
  /** 子内容 */
  children: React.ReactNode;
  /** 默认展开高度（像素） */
  collapsedHeight?: number;
  /** 展开按钮文本 */
  expandText?: string;
  /** 收起按钮文本 */
  collapseText?: string;
  /** 自定义样式类名 */
  className?: string;
  /** 按钮样式类名 */
  buttonClassName?: string;
  /** 是否默认展开 */
  defaultExpanded?: boolean;
  /** 渐变遮罩高度 */
  fadeHeight?: number;
}

const CollapsibleContent: React.FC<CollapsibleContentProps> = ({
  children,
  collapsedHeight = 300,
  expandText = "Show more",
  collapseText = "Show less", 
  className = '',
  buttonClassName = '',
  defaultExpanded = false,
  fadeHeight = 60
}) => {
  const [isExpanded, setIsExpanded] = useState(defaultExpanded);
  const [shouldShowButton, setShouldShowButton] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null);
  const [contentHeight, setContentHeight] = useState<number>(0);

  // 检查内容高度，决定是否显示展开/收起按钮
  useEffect(() => {
    const checkContentHeight = () => {
      if (contentRef.current) {
        const height = contentRef.current.scrollHeight;
        setContentHeight(height);
        setShouldShowButton(height > collapsedHeight);
      }
    };

    checkContentHeight();
    
    // 监听窗口大小变化，重新计算高度
    const handleResize = () => {
      setTimeout(checkContentHeight, 100);
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, [collapsedHeight, children]);

  // 切换展开/收起状态
  const handleToggle = () => {
    setIsExpanded(!isExpanded);
  };

  // 如果内容不够高，直接显示全部内容
  if (!shouldShowButton) {
    return (
      <div className={className}>
        <div ref={contentRef}>
          {children}
        </div>
      </div>
    );
  }

  return (
    <div className={`relative ${className}`}>
      {/* 内容区域 - 始终在DOM中，确保SEO友好 */}
      <div
        ref={contentRef}
        className={`
          transition-all duration-300 ease-in-out overflow-hidden
          ${isExpanded ? '' : 'relative'}
        `}
        style={{
          maxHeight: isExpanded ? 'none' : `${collapsedHeight}px`
        }}
        aria-expanded={isExpanded}
      >
        {children}
        
        {/* 渐变遮罩 - 只在收起状态显示 */}
        {!isExpanded && (
          <div 
            className="absolute bottom-0 left-0 right-0 pointer-events-none"
            style={{
              height: `${fadeHeight}px`,
              background: `linear-gradient(transparent, rgb(255 255 255 / 0.9) 70%, rgb(255 255 255))`
            }}
            aria-hidden="true"
          />
        )}
        
        {/* 深色模式渐变遮罩 */}
        {!isExpanded && (
          <div 
            className="absolute bottom-0 left-0 right-0 pointer-events-none dark:block hidden"
            style={{
              height: `${fadeHeight}px`,
              background: `linear-gradient(transparent, rgb(17 24 39 / 0.9) 70%, rgb(17 24 39))`
            }}
            aria-hidden="true"
          />
        )}
      </div>

      {/* 展开/收起按钮 */}
      <div className="flex justify-center mt-4">
        <button
          onClick={handleToggle}
          className={`
            inline-flex items-center gap-2 px-6 py-3 
            bg-blue-600 hover:bg-blue-700 
            text-white font-medium text-sm
            rounded-lg transition-all duration-200
            hover:shadow-md active:scale-95
            focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2
            dark:bg-blue-600 dark:hover:bg-blue-700
            ${buttonClassName}
          `}
          aria-label={isExpanded ? collapseText : expandText}
          type="button"
        >
          <span>{isExpanded ? collapseText : expandText}</span>
          {isExpanded ? (
            <ChevronUp className="w-4 h-4" />
          ) : (
            <ChevronDown className="w-4 h-4" />
          )}
        </button>
      </div>

      {/* 结构化数据标记 - 帮助搜索引擎理解内容结构 */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "WebPageElement",
            "name": "Collapsible Content Section",
            "description": "Interactive content section with expand/collapse functionality",
            "interactionStatistic": {
              "@type": "InteractionCounter",
              "interactionType": "https://schema.org/ViewAction",
              "userInteractionCount": 0
            }
          })
        }}
      />
    </div>
  );
};

export default CollapsibleContent; 