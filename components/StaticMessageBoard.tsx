"use client";

import React from 'react';
import WalineComments from './WalineComments';
import { useTheme } from 'next-themes';

interface StaticMessageBoardProps {
  /** 游戏slug，用于标识留言所属游戏 */
  gameSlug: string;
  /** 自定义样式类名 */
  className?: string;
  /** 是否显示功能说明 */
  showNotice?: boolean;
}

const StaticMessageBoard: React.FC<StaticMessageBoardProps> = ({ 
  gameSlug, 
  className = '',
  showNotice = false // 默认不显示旧的说明，因为现在有真实的评论系统
}) => {
  const { theme, systemTheme } = useTheme();
  
  // 安全地获取当前主题，避免 hydration 问题
  const currentTheme = theme === 'system' ? systemTheme : theme;
  
  return (
    <section 
      className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 ${className}`}
      role="region"
      aria-label="Game comment section"
    >
      <WalineComments 
        path={`/game/${gameSlug}`}
        dark={currentTheme === 'dark'}
        className="w-full"
      />
    </section>
  );
};

export default StaticMessageBoard; 