"use client";

import Link from "next/link";
import { <PERSON>u, Sidebar, SidebarO<PERSON>, SidebarClose } from "lucide-react";
import { useSidebar } from "@/components/Sidebar";

// Pokemon Gamma Emerald Logo组件
const PokemonLogo = () => {
  return (
    <div className="flex flex-col items-center">
      <div className="flex items-baseline space-x-1">
        {/* Pokemon 文字 */}
        <span className="text-2xl font-bold bg-gradient-to-r from-blue-600 via-blue-700 to-blue-800 bg-clip-text text-transparent">
          Pokemon
        </span>
        {/* gamma 文字 */}
        <span className="text-2xl font-bold bg-gradient-to-r from-red-500 via-orange-500 to-yellow-500 bg-clip-text text-transparent italic">
          gamma
        </span>
      </div>
      {/* emerald 文字 */}
      <span className="text-xl font-bold bg-gradient-to-r from-green-600 via-green-700 to-emerald-600 bg-clip-text text-transparent -mt-1">
        emerald
      </span>
    </div>
  );
};

const Header = () => {
  const { toggleSidebar, isOpen, isMobile } = useSidebar();

  return (
    <header 
      className={`
        fixed top-0 right-0 z-[60] border-b border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900
        transition-all duration-300 ease-in-out
        ${!isMobile && isOpen ? 'left-64' : 'left-0'}
      `}
    >
      <div className="w-full px-4">
        <nav className="relative flex items-center h-16">
          {/* 左侧 - 侧边栏切换按钮 */}
          <div className="flex items-center">
            <button
              onClick={toggleSidebar}
              className="flex items-center space-x-2 px-3 py-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors group"
              aria-label={isOpen ? "Close sidebar" : "Open sidebar"}
              title={isOpen ? "Close sidebar" : "Open sidebar"}
            >
              {isMobile ? (
                // 移动端使用汉堡菜单图标
                <>
                  <Menu className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-gray-200 transition-colors" />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors">
                    Menu
                  </span>
                </>
              ) : (
                // 桌面端使用侧边栏图标，根据状态显示不同图标
                <>
                  {isOpen ? (
                    <>
                      <SidebarClose className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-gray-200 transition-colors" />
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors">
                        Hide
                      </span>
                    </>
                  ) : (
                    <>
                      <SidebarOpen className="w-5 h-5 text-gray-600 dark:text-gray-400 group-hover:text-gray-900 dark:group-hover:text-gray-200 transition-colors" />
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300 group-hover:text-gray-900 dark:group-hover:text-gray-100 transition-colors">
                        Menu
                      </span>
                    </>
                  )}
                </>
              )}
            </button>
          </div>

          {/* 中央Logo */}
          <div className="flex-1 flex justify-center">
            <Link href="/" className="flex items-center hover:opacity-80 transition-opacity">
              <PokemonLogo />
            </Link>
          </div>
        </nav>
      </div>
    </header>
  );
};

export default Header;
