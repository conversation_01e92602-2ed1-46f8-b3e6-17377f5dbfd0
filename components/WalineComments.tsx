"use client";

import { useEffect, useRef, useState } from 'react';
import { init } from '@waline/client';
import '@waline/client/style';
import '@/styles/waline.css';

interface WalineCommentsProps {
  /** 页面路径，用于区分不同页面的评论 */
  path?: string;
  /** 自定义样式类名 */
  className?: string;
  /** 是否启用深色模式 */
  dark?: boolean;
}

const WalineComments: React.FC<WalineCommentsProps> = ({
  path,
  className = '',
  dark = false
}) => {
  const walineInstanceRef = useRef<any>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const [isMounted, setIsMounted] = useState(false);

  // 确保组件只在客户端渲染
  useEffect(() => {
    setIsMounted(true);
  }, []);

  useEffect(() => {
    if (!isMounted || !containerRef.current) return;

    // 销毁之前的实例
    if (walineInstanceRef.current) {
      walineInstanceRef.current.destroy?.();
    }

    // 初始化 Waline
    walineInstanceRef.current = init({
      // Waline 服务端地址
      serverURL: process.env.NEXT_PUBLIC_WALINE_SERVER_URL || 'https://pokemon-gamma-emerald-waline.vercel.app',
      
      // 挂载元素
      el: containerRef.current,
      
      // 页面路径
      path: path || (typeof window !== 'undefined' ? window.location.pathname : '/'),
      
      // 语言设置
      lang: 'en',
      
      // 表情包设置
      emoji: [
        '//unpkg.com/@waline/emojis@1.1.0/weibo',
        '//unpkg.com/@waline/emojis@1.1.0/alus',
        '//unpkg.com/@waline/emojis@1.1.0/bilibili',
      ],
      
      // 深色模式
      dark: dark ? 'auto' : false,
      
      // 评论者相关属性
      requiredMeta: ['nick'], // 只需要昵称，邮箱可选
      
      // 登录设置 - 允许匿名评论
      login: 'enable', // 'enable' | 'disable' | 'force'
      
      // 评论数统计
      comment: true,
      
      // 页面访问量统计
      pageview: true,
      
      // 本地化文本 - 英文界面
      locale: {
        nick: 'Nickname',
        mail: 'Email (Optional)',
        link: 'Website (Optional)',
        placeholder: 'Write your comment here...',
        sofa: 'No comments yet. Be the first to comment!',
        submit: 'Submit',
        like: 'Like',
        cancelLike: 'Cancel Like',
        reply: 'Reply',
        cancelReply: 'Cancel Reply',
        comment: 'Comment',
        refresh: 'Refresh',
        more: 'Load More...',
        preview: 'Preview',
        emoji: 'Emoji',
        uploadImage: 'Upload Image',
        seconds: 'seconds ago',
        minutes: 'minutes ago',
        hours: 'hours ago',
        days: 'days ago',
        now: 'just now',
        uploading: 'Uploading...',
        login: 'Login',
        logout: 'Logout',
        admin: 'Admin',
        sticky: 'Sticky',
        word: 'words',
        wordHint: 'Comment should be between $0 and $1 words!\nCurrent word count: $2',
        anonymous: 'Anonymous',
        level0: 'Newbie',
        level1: 'Explorer',
        level2: 'Active',
        level3: 'Regular',
        level4: 'Expert',
        level5: 'Legend',
        gif: 'GIF',
        gifSearchPlaceholder: 'Search GIF',
        profile: 'Profile',
        approved: 'Approved',
        waiting: 'Pending Review',
        spam: 'Spam',
        unsticky: 'Remove Sticky',
        oldest: 'Oldest',
        latest: 'Latest',
        hottest: 'Hottest',
        reactionTitle: 'What do you think of this article?',
      },
      
      // 搜索功能
      search: false,
      
      // 代码高亮
      highlighter: false,
      
      // 数学公式
      texRenderer: false,
      
      // 图片上传（可选，需要配置图床）
      imageUploader: false,
      
      // 评论排序
      commentSorting: 'latest',
      
      // 反垃圾设置
      wordLimit: [0, 1000], // 评论字数限制
      
      // 评论审核（可选）
      // moderation: true,
    });

    // 清理函数
    return () => {
      if (walineInstanceRef.current) {
        walineInstanceRef.current.destroy?.();
        walineInstanceRef.current = null;
      }
    };
  }, [isMounted, path, dark]);

  // 在服务端渲染时显示加载状态
  if (!isMounted) {
    return (
      <div className={`waline-container ${className}`}>
        {/* 评论区标题 */}
        <div className="mb-6">
          <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
            💬 Comments
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            Share your thoughts and feedback! Anonymous comments are welcome. Leave your email to get reply notifications.
          </p>
        </div>
        
        {/* 加载状态 */}
        <div className="flex justify-center items-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600 dark:text-gray-400">Loading comments...</span>
        </div>
      </div>
    );
  }

  return (
    <div className={`waline-container ${className}`}>
      {/* 评论区标题 */}
      <div className="mb-6">
        <h3 className="text-xl font-bold text-gray-900 dark:text-white mb-2">
          💬 Comments
        </h3>
        <p className="text-sm text-gray-600 dark:text-gray-400">
          Share your thoughts and feedback! Anonymous comments are welcome. Leave your email to get reply notifications.
        </p>
      </div>
      
      {/* Waline 评论组件挂载点 */}
      <div ref={containerRef} className="waline-wrapper" />
    </div>
  );
};

export default WalineComments; 