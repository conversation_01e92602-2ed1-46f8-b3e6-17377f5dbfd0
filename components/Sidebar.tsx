"use client";

import React, { useState, createContext, useContext, useEffect, useRef } from 'react';
import Link from 'next/link';
import { 
  Gamepad2, 
  House, 
  Clock, 
  TrendingUp, 
  Heart,
  Search,
  X
} from 'lucide-react';
import { ThemeToggle } from "@/components/ThemeToggle";

// 创建侧边栏状态上下文
interface SidebarContextType {
  isOpen: boolean;
  toggleSidebar: () => void;
  closeSidebar: () => void;
  isMobile: boolean;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export const useSidebar = () => {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
};

// 检测移动端设备
const useIsMobile = () => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkIsMobile = () => {
      setIsMobile(window.innerWidth < 1024); // lg断点
    };

    // 初始检查
    checkIsMobile();

    // 监听窗口大小变化
    window.addEventListener('resize', checkIsMobile);
    return () => window.removeEventListener('resize', checkIsMobile);
  }, []);

  return isMobile;
};

// 触摸手势钩子
const useSwipeGesture = (onSwipeRight: () => void, onSwipeLeft: () => void) => {
  const touchStartX = useRef<number>(0);
  const touchStartY = useRef<number>(0);

  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.touches[0].clientX;
    touchStartY.current = e.touches[0].clientY;
  };

  const handleTouchEnd = (e: React.TouchEvent) => {
    if (!touchStartX.current || !touchStartY.current) return;

    const touchEndX = e.changedTouches[0].clientX;
    const touchEndY = e.changedTouches[0].clientY;
    
    const deltaX = touchEndX - touchStartX.current;
    const deltaY = touchEndY - touchStartY.current;
    
    // 确保是水平滑动且滑动距离足够
    if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
      if (deltaX > 0) {
        onSwipeRight();
      } else {
        onSwipeLeft();
      }
    }

    touchStartX.current = 0;
    touchStartY.current = 0;
  };

  return { handleTouchStart, handleTouchEnd };
};

// 侧边栏提供者组件
export const SidebarProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const isMobile = useIsMobile();
  const [isOpen, setIsOpen] = useState(false); // 默认关闭，特别是移动端

  const toggleSidebar = () => {
    setIsOpen(!isOpen);
  };

  const closeSidebar = () => {
    setIsOpen(false);
  };

  const openSidebar = () => {
    setIsOpen(true);
  };

  // 当切换到桌面端时，自动打开侧边栏
  useEffect(() => {
    if (!isMobile) {
      setIsOpen(true);
    } else {
      setIsOpen(false); // 移动端默认关闭
    }
  }, [isMobile]);

  // 移动端阻止滚动穿透
  useEffect(() => {
    if (isMobile && isOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'unset';
    }

    return () => {
      document.body.style.overflow = 'unset';
    };
  }, [isMobile, isOpen]);

  // 添加全局触摸手势支持
  const { handleTouchStart, handleTouchEnd } = useSwipeGesture(
    () => {
      if (isMobile && !isOpen) openSidebar();
    },
    () => {
      if (isMobile && isOpen) closeSidebar();
    }
  );

  return (
    <SidebarContext.Provider value={{ isOpen, toggleSidebar, closeSidebar, isMobile }}>
      <div onTouchStart={handleTouchStart} onTouchEnd={handleTouchEnd}>
        {children}
      </div>
    </SidebarContext.Provider>
  );
};

interface SidebarProps {
  className?: string;
}

const Sidebar: React.FC<SidebarProps> = ({ className = '' }) => {
  const { isOpen, toggleSidebar, closeSidebar, isMobile } = useSidebar();

  return (
    <>
      {/* 移动端遮罩层 */}
      {isMobile && isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-[45] lg:hidden transition-opacity duration-300"
          onClick={closeSidebar}
          aria-label="Close sidebar"
        />
      )}

      {/* 侧边栏 */}
      <aside 
        className={`
          fixed top-0 left-0 h-screen bg-white dark:bg-gray-900 
          border-r border-gray-200 dark:border-gray-800 
          transition-all duration-300 ease-in-out
          ${isMobile 
            ? `w-80 ${isOpen ? 'translate-x-0' : '-translate-x-full'} z-50` 
            : `${isOpen ? 'w-64' : 'w-0'} overflow-hidden z-40`
          }
          ${className}
        `}
      >
        {/* 移动端顶部栏 */}
        {isMobile && (
          <div className="flex items-center justify-between p-4 border-b border-gray-200 dark:border-gray-700">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-white">Menu</h2>
            <button
              onClick={closeSidebar}
              className="p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              aria-label="Close menu"
            >
              <X className="w-5 h-5 text-gray-600 dark:text-gray-400" />
            </button>
          </div>
        )}

        {/* 为页眉留出空间（桌面端）或从顶部开始（移动端） */}
        <div className={`${isMobile ? 'h-full' : 'pt-16 h-full'} overflow-y-auto`}>
          <div className="p-6">
            {/* 搜索框和主题切换区域 */}
            <div className="mb-8">
              <div className="flex items-center gap-3">
                <div className="relative flex-1">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg bg-white dark:bg-gray-800 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent text-sm"
                  />
                </div>
                <div className="flex-shrink-0">
                  <ThemeToggle />
                </div>
              </div>
            </div>

            {/* 导航菜单 */}
            <nav className="space-y-6">
              {/* 主要导航 */}
              <div>
                <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3">
                  Navigation
                </div>
                <ul className="space-y-2">
                  <li>
                    <Link
                      href="/"
                      onClick={() => isMobile && closeSidebar()}
                      className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                    >
                      <House className="w-5 h-5" />
                      <span className="font-medium">Home</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/favorites"
                      onClick={() => isMobile && closeSidebar()}
                      className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                    >
                      <Heart className="w-5 h-5" />
                      <span className="font-medium">Favorites</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/new-games"
                      onClick={() => isMobile && closeSidebar()}
                      className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                    >
                      <Clock className="w-5 h-5" />
                      <span className="font-medium">New Games</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/hot-games"
                      onClick={() => isMobile && closeSidebar()}
                      className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                    >
                      <TrendingUp className="w-5 h-5" />
                      <span className="font-medium">Hot Games</span>
                    </Link>
                  </li>
                </ul>
              </div>

              {/* 分隔线 */}
              <div className="border-t border-gray-200 dark:border-gray-700"></div>

              {/* 游戏分类 */}
              <div>
                <div className="text-xs font-semibold text-gray-500 dark:text-gray-400 uppercase tracking-wide mb-3">
                  Game Genres
                </div>
                <ul className="space-y-2">
                  <li>
                    <Link
                      href="/games/rpg-games"
                      onClick={() => isMobile && closeSidebar()}
                      className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                    >
                      <Gamepad2 className="w-4 h-4" />
                      <span className="font-medium">RPG Games</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/games/adventure-games"
                      onClick={() => isMobile && closeSidebar()}
                      className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                    >
                      <Gamepad2 className="w-4 h-4" />
                      <span className="font-medium">Adventure Games</span>
                    </Link>
                  </li>
                  <li>
                    <Link
                      href="/games/pokemon-games"
                      onClick={() => isMobile && closeSidebar()}
                      className="flex items-center space-x-3 px-3 py-2 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                    >
                      <Gamepad2 className="w-4 h-4" />
                      <span className="font-medium">Pokemon Games</span>
                    </Link>
                  </li>
                </ul>
              </div>
            </nav>
          </div>
        </div>
      </aside>


    </>
  );
};

export default Sidebar; 