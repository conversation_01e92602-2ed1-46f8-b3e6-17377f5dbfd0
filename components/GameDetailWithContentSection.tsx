"use client";

import React from 'react';
import { Game } from '@/lib/types';
import GamePlayer from './GamePlayer';
import GameContentSection from './GameContentSection';

interface GameDetailWithContentSectionProps {
  game: Game;
  /** 是否显示游戏播放器 */
  showGamePlayer?: boolean;
  /** 是否显示面包屑导航 */
  showBreadcrumbs?: boolean;
  /** 是否显示游戏元信息 */
  showGameMeta?: boolean;
  /** 自定义容器样式 */
  className?: string;
}

export default function GameDetailWithContentSection({ 
  game,
  showGamePlayer = true,
  showBreadcrumbs = true,
  showGameMeta = true,
  className = ''
}: GameDetailWithContentSectionProps) {
  // 错误处理
  if (!game) {
    return (
      <div className={`max-w-6xl mx-auto p-6 ${className}`}>
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6 text-center">
          <h2 className="text-xl font-semibold text-red-800 dark:text-red-200 mb-2">
            游戏未找到
          </h2>
          <p className="text-red-600 dark:text-red-300">
            无法加载请求的游戏，请稍后重试。
          </p>
        </div>
      </div>
    );
  }

  // 游戏播放器事件处理
  const handleFavorite = (isFavorited: boolean) => {
    console.log(`游戏 ${game.title} ${isFavorited ? '已添加到' : '已从'}收藏夹`);
    // 这里可以添加实际的收藏逻辑
  };

  const handleShare = () => {
    console.log(`分享游戏: ${game.title}`);
    // 这里可以添加实际的分享逻辑
  };

  const handleFullscreen = () => {
    console.log(`${game.title} 切换全屏模式`);
    // 这里可以添加实际的全屏逻辑
  };

  return (
    <div className={`min-h-screen bg-white dark:bg-gray-900 ${className}`}>
      {/* 游戏播放器区域 */}
      {showGamePlayer && game.iframeUrl && (
        <section className="py-8 bg-gray-50 dark:bg-gray-800">
          <div className="container mx-auto px-4">
            <GamePlayer
              title={game.title}
              iframeUrl={game.iframeUrl}
              gameSlug={game.slug}
              gameData={{
                title: game.title,
                slug: game.slug,
                thumbnail: game.thumbnail,
                genres: game.genres,
                description: game.description,
                iframeUrl: game.iframeUrl,
                publishedDate: game.publishedDate,
                author: game.author,
                version: game.version,
                pageDescription: game.pageDescription
              }}
              className="w-full max-w-5xl mx-auto"
              onFavorite={handleFavorite}
              onShare={handleShare}
              onFullscreen={handleFullscreen}
            />
          </div>
        </section>
      )}

      {/* 游戏内容详情区域 */}
      <section className="py-8">
        <div className="container mx-auto px-4">
          <GameContentSection 
            game={game}
            showGameMeta={showGameMeta}
            customBreadcrumbs={showBreadcrumbs ? undefined : []}
            className="max-w-4xl mx-auto bg-white dark:bg-gray-900"
            contentClassName="prose-lg prose-gray dark:prose-invert max-w-none"
          />
        </div>
      </section>
    </div>
  );
}

// 导出类型定义
export type { GameDetailWithContentSectionProps }; 