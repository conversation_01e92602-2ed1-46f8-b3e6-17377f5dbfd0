"use client";

import React from 'react';
import GameCard, { GameCardSkeleton } from './GameCard';
import { GameFrontmatter } from '@/lib/types';

interface VerticalGameCardsProps {
  /** 游戏数据列表 */
  games: GameFrontmatter[];
  /** 区域标题 */
  title?: string;
  /** 最大显示数量 */
  maxItems?: number;
  /** 自定义样式类名 */
  className?: string;
}

// 占位卡片组件
function PlaceholderCards({ count = 2 }: { count?: number }) {
  return (
    <>
      {Array.from({ length: count }).map((_, index) => (
        <div key={`placeholder-${index}`} className="mb-4 last:mb-0">
          <GameCardSkeleton size="medium" />
        </div>
      ))}
    </>
  );
}

export default function VerticalGameCards({
  games = [],
  title,
  maxItems = 4,
  className = ''
}: VerticalGameCardsProps) {
  // 处理显示的游戏数据
  const displayGames = maxItems ? games.slice(0, maxItems) : games;
  
  // 计算需要的占位符数量
  const placeholderCount = Math.max(0, maxItems - displayGames.length);

  return (
    <div className={`vertical-games-container ${className}`}>
      {/* 标题区域 */}
      {title && (
        <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
          {title}
        </h2>
      )}

      {/* 游戏卡片列表 */}
      <div className="space-y-4">
        {/* 实际游戏卡片 */}
        {displayGames.map((game, index) => (
          <div key={game.slug} className="game-card-wrapper">
            <GameCard
              game={game}
              size="medium"
              tags={index === 0 ? ['hot'] : []}
              lazy={index > 1}
            />
          </div>
        ))}

        {/* 占位卡片 */}
        {placeholderCount > 0 && (
          <PlaceholderCards count={placeholderCount} />
        )}
      </div>
    </div>
  );
}