"use client";

import Link from "next/link";
import { useSidebar } from "@/components/Sidebar";

const Footer = () => {
  const currentYear = new Date().getFullYear();
  const { isOpen, isMobile } = useSidebar();

  return (
    <footer 
      className={`
        bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 py-10
        transition-all duration-300 ease-in-out
        ${!isMobile && isOpen ? 'ml-64' : 'ml-0'}
      `}
    >
      <div className="max-w-7xl mx-auto px-4 text-center">
        {/* Navigation Links */}
        <nav className="grid grid-flow-col gap-4 justify-center mb-8">
          <Link href="/about-us" className="link hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
            About Us
          </Link>
          <Link href="/contact-us" className="link hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
            Contact Us
          </Link>
          <Link href="/privacy-policy" className="link hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
            Privacy Policy
          </Link>
          <Link href="/term-of-use" className="link hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
            Terms of Use
          </Link>
          <Link href="/copyright-infringement-notice-procedure" className="link hover:text-blue-600 dark:hover:text-blue-400 transition-colors">
            Copyright Notice
          </Link>
        </nav>

        {/* Social Media Links */}
        <nav className="mb-8">
          <div className="grid grid-flow-col gap-4 justify-center">
            {/* Twitter/X */}
            <a 
              href="https://twitter.com/intent/tweet?text=Check%20out%20this%20amazing%20Pokemon%20gaming%20site!&url=https://pokemon-gamma-emerald.com" 
              target="_blank" 
              rel="noopener noreferrer"
              className="hover:text-blue-400 transition-colors"
              aria-label="Share on Twitter"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                className="fill-current">
                <path
                  d="M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"></path>
              </svg>
            </a>

            {/* YouTube */}
            <a 
              href="https://www.youtube.com/results?search_query=pokemon+gamma+emerald+gameplay" 
              target="_blank" 
              rel="noopener noreferrer"
              className="hover:text-red-500 transition-colors"
              aria-label="Watch on YouTube"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                className="fill-current">
                <path
                  d="M19.615 3.184c-3.604-.246-11.631-.245-15.23 0-3.897.266-4.356 2.62-4.385 8.816.029 6.185.484 8.549 4.385 8.816 3.6.245 11.626.246 15.23 0 3.897-.266 4.356-2.62 4.385-8.816-.029-6.185-.484-8.549-4.385-8.816zm-10.615 12.816v-8l8 3.993-8 4.007z"></path>
              </svg>
            </a>

            {/* Facebook */}
            <a 
              href="https://www.facebook.com/sharer/sharer.php?u=https://pokemon-gamma-emerald.com" 
              target="_blank" 
              rel="noopener noreferrer"
              className="hover:text-blue-600 transition-colors"
              aria-label="Share on Facebook"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                className="fill-current">
                <path
                  d="M9 8h-3v4h3v12h5v-12h3.642l.358-4h-4v-1.667c0-.955.192-1.333 1.115-1.333h2.885v-5h-3.808c-3.596 0-5.192 1.583-5.192 4.615v3.385z"></path>
              </svg>
            </a>

            {/* Reddit */}
            <a 
              href="https://www.reddit.com/submit?url=https://pokemon-gamma-emerald.com&title=Amazing%20Pokemon%20Gaming%20Site!" 
              target="_blank" 
              rel="noopener noreferrer"
              className="hover:text-orange-500 transition-colors"
              aria-label="Share on Reddit"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                className="fill-current">
                <path
                  d="M12 0A12 12 0 0 0 0 12a12 12 0 0 0 12 12 12 12 0 0 0 12-12A12 12 0 0 0 12 0zm5.01 4.744c.688 0 1.25.561 1.25 1.249a1.25 1.25 0 0 1-2.498.056l-2.597-.547-.8 3.747c1.824.07 3.48.632 4.674 1.488.308-.309.73-.491 1.207-.491.968 0 1.754.786 1.754 1.754 0 .716-.435 1.333-1.01 1.614a3.111 3.111 0 0 1 .042.52c0 2.694-3.13 4.87-7.004 4.87-3.874 0-7.004-2.176-7.004-4.87 0-.183.015-.366.043-.534A1.748 1.748 0 0 1 4.028 12c0-.968.786-1.754 1.754-1.754.463 0 .898.196 1.207.49 1.207-.883 2.878-1.43 4.744-1.487l.885-4.182a.342.342 0 0 1 .14-.197.35.35 0 0 1 .238-.042l2.906.617a1.214 1.214 0 0 1 1.108-.701zM9.25 12C8.561 12 8 12.562 8 13.25c0 .687.561 1.248 1.25 1.248.687 0 1.248-.561 1.248-1.249 0-.688-.561-1.249-1.249-1.249zm5.5 0c-.687 0-1.248.561-1.248 1.25 0 .687.561 1.248 1.249 1.248.688 0 1.249-.561 1.249-1.249 0-.687-.562-1.249-1.25-1.249zm-5.466 3.99a.327.327 0 0 0-.231.094.33.33 0 0 0 0 .463c.842.842 2.484.913 2.961.913.477 0 2.105-.056 2.961-.913a.361.361 0 0 0 .029-.463.33.33 0 0 0-.464 0c-.547.533-1.684.73-2.512.73-.828 0-1.979-.196-2.512-.73a.326.326 0 0 0-.232-.095z"></path>
              </svg>
            </a>
          </div>
        </nav>

        {/* Copyright and Disclaimer */}
        <aside>
          <p className="text-sm mb-2">
            © {currentYear} pokemon-gamma-emerald.com - Made with ❤️ by a passionate Pokémon fan
          </p>
          <p className="text-xs text-gray-500 dark:text-gray-400">
            This is a fan-made website. Pokémon and all related characters are trademarks of Nintendo, Game Freak, and The Pokémon Company.
          </p>
        </aside>
      </div>
    </footer>
  );
};

export default Footer;
