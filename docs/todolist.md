# 开发任务清单 (todolist.md) - 游戏在线体验站

**版本：1.0**
**最后更新日期：2025-01-27**

## 正式任务区

### Phase 1: 项目清理与基础结构搭建

#### 1.1 项目清理
- [x] 删除不需要的目录和文件（stores/, app/blogs/, components/social-icons/, components/home/<USER>/TailwindIndicator.tsx, types/siteConfig.ts）
- [x] 重构 app/about/ 为 app/about-us/
- [x] 创建 .env 文件并配置必要的环境变量
- [x] 验证项目清理后能正常启动

#### 1.2 目录结构创建
- [x] 创建 games/ 目录用于存放游戏 Markdown 文件
- [x] 创建 config/ 目录
- [x] 创建 public/images/thumbnails/ 目录用于存放游戏缩略图
- [x] 重构 app/ 目录结构，创建 (home)/, [game-slug]/, new-games/, hot-games/, games/[genre]-games/ 等子目录
- [x] 创建所需的静态页面目录（about-us/, contact-us/, privacy-policy/, term-of-use/, copyright-infringement-notice-procedure/）

### Phase 2: TypeScript 接口定义与 Markdown 处理

#### 2.1 TypeScript 接口定义
- [x] 在 lib/ 目录下创建 types.ts，定义 GameFrontmatter 接口
- [x] 在 types.ts 中定义 Game 接口（继承 GameFrontmatter 并添加 contentHtml）
- [x] 验证接口定义的完整性和正确性

#### 2.2 Markdown 处理逻辑实现
- [x] 安装必要的依赖包（gray-matter, remark, remark-html 等）
- [x] 实现 lib/markdown.ts 中的 getGameSlugs() 函数
- [x] 实现 lib/markdown.ts 中的 getGameBySlug() 函数
- [x] 实现 lib/markdown.ts 中的 getAllGameFrontmatters() 函数（支持排序和分类过滤）
- [x] 实现 lib/markdown.ts 中的 getHotGameFrontmatters() 函数
- [x] 实现 lib/markdown.ts 中的 getCoreGenres() 函数（读取 config/core-genres.json）
- [x] 创建测试用的示例游戏 Markdown 文件（至少3个不同类型的游戏）
- [x] 验证所有 Markdown 处理函数的正确性

#### 2.3 配置文件创建
- [x] 创建 config/hot-games.json 配置文件，包含热门游戏的 slug 列表
- [x] 创建 config/core-genres.json 配置文件，定义预设核心分类列表（如：["RPG", "Adventure", "Action", "Puzzle"]）
- [x] 验证配置文件格式的正确性

### Phase 3: 核心组件实现

#### 3.1 GameCard 组件
- [x] 实现 components/GameCard.tsx 组件，接收 GameFrontmatter 作为 props
- [x] 实现正方形样式布局
- [x] 集成 Next.js Image 组件显示缩略图
- [x] 添加游戏标题、主要类型标签显示
- [x] 添加 Link 组件包装，指向游戏详情页
- [x] 使用 Tailwind CSS 实现响应式设计
- [x] 验证组件在不同屏幕尺寸下的显示效果

#### 3.2 GameDetail 组件
- [x] 实现 components/GameDetail.tsx 组件，接收 Game 作为 props
- [x] 实现游戏标题显示
- [x] 实现 iframe 嵌入功能，显示 iframeUrl 内容
- [x] 实现游戏元信息显示（类型标签、发布日期、描述等）
- [x] 实现 Markdown 内容渲染（使用 dangerouslySetInnerHTML）
- [x] 添加适当的样式和布局
- [x] 验证组件能正确显示完整游戏信息

#### 3.3 导航组件重构
- [x] 重构 components/header/ 下的 Header 组件，符合 ERD 要求
- [x] 实现 Header 中的 Logo、核心页面链接、预设分类链接
- [x] 在 Header 中添加搜索功能占位符（按钮或输入框，暂不实现功能）
- [x] 在 Header 中添加主题切换功能占位符（保留现有 ThemeToggle 组件但确保样式一致）
- [x] 重构 components/Sidebar.tsx 组件，包含所有导航链接
- [x] 重构 components/footer/ 下的 Footer 组件，包含静态页面链接
- [x] 验证所有导航链接的正确性

### Phase 4: 页面功能实现

#### 4.1 首页实现
- [x] 实现 app/(home)/page.tsx，硬编码指定核心游戏 slug
- [x] 集成 getGameBySlug() 函数获取核心游戏数据
- [x] 使用 GameDetail 组件渲染游戏详情
- [x] 添加适当的 SEO 元数据
- [x] 验证首页能正确显示指定的核心游戏

#### 4.2 动态游戏详情页
- [x] 实现 app/[game-slug]/page.tsx 动态路由
- [x] 实现 generateStaticParams 函数，基于 getGameSlugs()
- [x] 集成 getGameBySlug() 函数获取游戏数据
- [x] 处理游戏不存在的情况（404页面）
- [x] 使用 GameDetail 组件渲染
- [x] 添加动态 SEO 元数据
- [x] 验证动态路由能正确工作

#### 4.3 游戏列表页面
- [x] 实现 app/new-games/page.tsx，显示按日期排序的所有游戏
- [x] 集成 getAllGameFrontmatters({ sortByDate: true }) 函数
- [x] 使用 GameCard 组件以网格形式展示游戏列表
- [x] 实现 app/hot-games/page.tsx，显示热门游戏列表
- [x] 集成 getHotGameFrontmatters() 函数
- [x] 添加适当的页面标题和描述
- [x] 验证列表页面的排序和显示正确性

#### 4.4 分类游戏页面
- [x] 实现 app/games/[genre]-games/page.tsx 动态分类路由
- [x] 实现 generateStaticParams 函数，基于 config/core-genres.json 预构建核心分类页面
- [x] 集成 getAllGameFrontmatters({ filterByGenre, sortByDate: true }) 函数
- [x] 使用 GameCard 组件展示分类游戏列表
- [x] 处理分类不存在或无游戏的情况
- [x] 添加分类特定的 SEO 元数据
- [x] 验证分类过滤功能的正确性

### Phase 5: 静态页面与最终完善

#### 5.1 静态页面实现
- [x] 实现 app/about-us/page.tsx，添加占位符内容
- [x] 实现 app/contact-us/page.tsx，添加占位符内容
- [x] 实现 app/privacy-policy/page.tsx，添加占位符内容
- [x] 实现 app/term-of-use/page.tsx，添加占位符内容
- [x] 实现 app/copyright-infringement-notice-procedure/page.tsx，添加占位符内容
- [ ] 实现 app/blog/[slug]/page.tsx，添加占位符内容（MVP阶段暂不实现具体功能）
- [x] 为所有静态页面添加适当的 SEO 元数据
- [x] 验证所有静态页面能正确访问

#### 5.2 SEO 优化与元数据
- [ ] 优化 app/layout.tsx 中的全局元数据
- [ ] 为所有页面添加适当的 title 和 description
- [ ] 实现 Open Graph 和 Twitter Card 元数据
- [ ] 优化 robots.txt 和 sitemap.xml
- [ ] 验证所有页面的 SEO 元数据正确性

#### 5.3 样式优化与响应式设计
- [ ] 优化全局样式，确保一致的视觉风格
- [ ] 完善响应式设计，确保在移动设备上的良好体验
- [ ] 优化游戏卡片的正方形布局在不同屏幕尺寸下的表现
- [ ] 优化 iframe 嵌入的响应式行为
- [ ] 验证在不同设备和浏览器上的兼容性

#### 5.4 性能优化与最终测试
- [ ] 优化图片加载性能（Next.js Image 组件配置）
- [ ] 优化 Markdown 处理性能
- [ ] 实现适当的错误处理和加载状态
- [ ] 进行全站功能测试
- [ ] 验证所有链接和导航的正确性
- [ ] 验证所有数据获取和渲染的正确性

### Phase 6: 紧急错误修复与稳定性优化

#### 6.1 TypeScript兼容性修复
- [x] 将 gtag.js 转换为 gtag.ts 并添加适当的类型定义
- [x] 修复 GoogleAnalytics 组件中的 TypeScript 导入问题
- [x] 验证所有 TypeScript 文件的类型安全性

#### 6.2 React组件错误修复
- [x] 检查并修复 React 组件中的 undefined 属性访问错误
- [x] 优化组件的异步渲染逻辑
- [x] 添加适当的空值检查和默认值处理

#### 6.3 组件一致性修复
- [x] 修复 Header 组件中硬编码的 genres 数组，改为使用 getCoreGenres() 函数
- [x] 确保所有组件使用统一的数据获取方式
- [x] 验证组件间的数据一致性

#### 6.4 错误处理与稳定性提升
- [x] 创建并实现 React 错误边界组件
- [x] 在关键页面中集成错误边界
- [x] 添加全局错误处理机制
- [x] 改善开发环境的错误日志可读性

#### 6.5 构建与兼容性验证
- [x] 验证项目在 Next.js 15 下的完整构建
- [x] 修复任何构建时的警告和错误
- [x] 测试所有页面的正常渲染和功能
- [x] 验证生产环境构建的稳定性

#### 6.6 Next.js 15客户端组件兼容性修复
- [x] 修复GameCard组件的客户端组件兼容性
- [x] 修复GameDetail组件的客户端组件兼容性
- [x] 重构图片错误处理策略
- [x] 优化iframe错误处理机制
- [x] 验证所有组件的服务器/客户端组件分离
- [x] 实现Next.js 15兼容的错误处理模式

### Phase 7: SSR错误修复与稳定性增强

#### 7.1 服务器端渲染(SSR)错误修复
- [x] 修复Header组件中getCoreGenres同步调用导致的SSR问题
- [x] 修复Sidebar组件中的数据一致性问题
- [x] 修复app/games/[genre]-games页面中的getCoreGenres调用问题
- [x] 统一所有组件使用硬编码的CORE_GENRES数组

#### 7.2 组件错误处理增强
- [x] 改进ErrorBoundary组件的错误显示和恢复机制
- [x] 优化WebsiteLogo组件的Image使用和Hook依赖
- [x] 创建useErrorHandler自定义Hook用于全局错误处理
- [x] 添加全局错误监听器处理未捕获的Promise rejections

#### 7.3 构建优化与验证
- [x] 解决所有TypeScript编译错误
- [x] 修复React Hook依赖警告
- [x] 优化组件性能和内存使用
- [x] 验证项目构建成功并生成17个静态页面

### Phase 8: 代码清理与优化

#### 8.1 未使用依赖包清理
- [x] 移除未使用的npm依赖包：@types/js-cookie, axios, dayjs, js-cookie, next-mdx-remote, react-hot-toast, remark-gfm, zod, zustand
- [ ] 移除未使用的devDependencies：autoprefixer, postcss（注意：需要验证Tailwind CSS是否真的不需要这些）
- [ ] 移除多余的依赖包：@emnapi/runtime（标记为extraneous）
- [x] 更新package.json并运行npm install清理node_modules
- [x] 验证项目在移除依赖后仍能正常构建和运行

#### 8.2 未使用TypeScript导出清理
- [ ] 清理components/GameCardExamples.tsx中的未使用导出函数（BasicGameGrid, DifferentSizesExample等8个示例函数）
- [ ] 清理components/GameContentSectionExamples.tsx中的未使用导出
- [ ] 清理components/GamePlayerExample.tsx中的未使用示例函数（4个示例函数）
- [ ] 清理components/RelatedGamesCarouselExamples.tsx中的未使用示例函数（10个示例函数）
- [ ] 清理lib/markdown.ts中的未使用导出：removeFirstH1FromContent, getCoreGenres
- [x] 清理lib/logger.ts中完全未被使用的logger模块

#### 8.3 未使用组件和文件清理
- [x] 删除完全未被使用的示例组件文件：
  - components/GameCardExamples.tsx
  - components/GameContentSectionExamples.tsx  
  - components/GamePlayerExample.tsx
  - components/RelatedGamesCarouselExamples.tsx
  - components/GameRecommendSectionExamples.tsx
- [x] 删除未被使用的README文档文件：
  - components/README-GameCard.md
  - components/README-GameContentSection.md
  - components/README-GamePlayer.md
  - components/README-RelatedGamesCarousel.md
  - components/README-StaticMessageBoard.md
- [x] 删除未被使用的日志模块：lib/logger.ts
- [x] 删除未被使用的错误处理Hook：hooks/useErrorHandler.ts

#### 8.4 未使用静态资源清理
- [x] 删除未被引用的图片文件：
  - public/images/thumbnails/pokemon-adventure.svg（只有.png版本被引用）
  - public/images/thumbnails/mario-world.png（0字节空文件）
  - public/images/thumbnails/pokemon-adventure.png（0字节空文件）
  - public/images/thumbnails/zelda-quest.png（0字节空文件）
- [x] 验证public/images/thumbnails/image.png是否真的被pokemon-gamma-emerald.md使用
- [x] 清理.next/cache目录中的构建缓存（可选，用于释放磁盘空间）

#### 8.5 未使用UI组件清理
- [ ] 清理components/ui/alert.tsx中未被使用的导出：Alert, AlertTitle, AlertDescription
- [ ] 清理components/ui/dropdown-menu.tsx中未被使用的导出（多个组件）
- [ ] 清理components/ui/select.tsx中未被使用的导出（多个组件）
- [ ] 清理components/ui/toaster.tsx中未被使用的Toaster组件
- [ ] 清理components/ui/toast.tsx中未被使用的ToastAction组件
- [ ] 清理hooks/use-toast.ts中未被使用的toast函数和reducer

#### 8.6 未使用图标和工具组件清理
- [x] 清理components/icons/index.tsx中未被使用的导出：LoadingDots, LoadingCircle, LoadingSpinner, ExpandingArrow
- [x] 清理components/icons/eye.tsx（完全未被使用）
- [x] 清理components/icons/sun.tsx和moon.tsx（如果ThemeToggle不使用的话）
- [ ] 清理components/WebsiteLogo.tsx中未被使用的导出

#### 8.7 配置文件和类型定义清理
- [x] 清理lib/types.ts中未被使用的类型定义：CoreGenresConfig
- [ ] 清理config/site.ts中未被使用的导出：BASE_URL
- [ ] 验证gtag.ts中的pageview和event函数是否真的被使用
- [ ] 清理middleware.ts中未被使用的导出（如果确实未被使用）

#### 8.8 清理验证与测试
- [x] 运行完整的项目构建测试，确保清理后项目仍能正常工作
- [x] 验证所有页面路由仍能正常访问
- [x] 验证所有保留的组件功能正常
- [x] 运行TypeScript类型检查，确保没有类型错误
- [x] 测试开发环境和生产环境构建
- [x] 更新项目文档，反映清理后的项目结构

### Phase 9: 关键缺失功能补充

#### 9.1 用户体验优化
- [x] 创建用户友好的自定义404错误页面（app/not-found.tsx） - [User Experience: 减少跳出率，引导用户继续浏览]
- [x] 完善sitemap.ts中的动态游戏页面生成逻辑（取消注释并实现） - [SEO: 确保所有游戏页面都能被搜索引擎发现]

#### 9.2 性能和配置优化
- [x] 优化next.config.mjs配置，添加图片域名配置和性能优化选项 - [Performance: 利用Next.js的优化特性]
- [x] 移除生产代码中的console.log语句（特别是lib/markdown.ts中的console.warn和console.error） - [Performance & Clean Code: 保持控制台清洁]

#### 9.3 代码清理（可选）
- [x] 清理components/ui/中未被使用的组件导出（alert.tsx, toast.tsx等） - [Code Quality: 减少包体积]
- [x] 清理hooks/use-toast.ts中未被使用的toast函数（如果确实未使用） - [Code Quality: 减少包体积]

### Phase 10: 项目完善验证

#### 10.1 最终验证与测试
- [x] 运行完整的项目构建测试，确保所有功能正常工作
- [x] 验证所有页面路由能正常访问
- [x] 验证SEO元数据在所有页面正确显示
- [x] 测试开发环境和生产环境构建
- [x] 进行跨浏览器兼容性测试

#### 10.2 文档更新
- [x] 更新README.md，反映当前项目的完整功能
- [x] 更新项目文档，记录所有已实现的功能
- [x] 创建部署指南和维护文档

## 建议任务区

### 未来功能扩展（非当前MVP必需）
以下任务为未来功能扩展，当前MVP阶段不需要实现：

#### 高级SEO功能
- [ ] 实现面包屑导航并添加BreadcrumbList结构化数据
- [ ] 为游戏详情页面添加更详细的VideoGame类型结构化数据验证
- [ ] 实现游戏标签页面的动态路由和SEO优化

#### 高级性能优化
- [ ] 对大型组件使用next/dynamic进行动态导入
- [ ] 实现WebP格式图片支持和自动转换
- [ ] 为getGameSlugs等函数实现内存缓存机制
- [ ] 实现ISR（增量静态再生）策略优化动态内容更新

#### 监控和分析
- [ ] 集成Web Vitals监控和报告机制
- [ ] 实现错误监控和报告系统（如Sentry集成）
- [ ] 添加用户行为分析和游戏使用统计

#### 安全性增强
- [ ] 实现Content Security Policy (CSP)头部配置
- [ ] 为iframe嵌入添加安全性验证和沙箱属性
- [ ] 实现环境变量的安全验证和默认值处理

#### 可访问性改进
- [ ] 为所有交互元素添加适当的ARIA标签
- [ ] 实现键盘导航支持的全面测试和优化
- [ ] 检查和改善色彩对比度，确保符合WCAG标准

#### 高级用户体验
- [ ] 在游戏详情页面添加相关游戏的内部链接
- [ ] 在列表页面添加分页功能和相应的SEO优化
- [ ] 实现游戏搜索功能
- [ ] 添加用户收藏和评分功能 