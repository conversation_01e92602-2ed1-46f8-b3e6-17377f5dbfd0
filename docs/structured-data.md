# 结构化数据实现文档

## 概述

本项目已实现完整的结构化数据（JSON-LD）支持，以提升SEO表现和搜索引擎理解能力。

## 实现的结构化数据类型

### 1. VideoGame Schema
- **位置**: 游戏详情页面和首页
- **用途**: 描述游戏信息，包括名称、描述、类型、发布日期等
- **包含字段**:
  - 基本信息：name, description, image, url
  - 游戏属性：genre, datePublished, version, gamePlatform
  - 商业信息：offers, aggregateRating
  - 技术信息：operatingSystem, softwareRequirements

### 2. BreadcrumbList Schema
- **位置**: 所有页面（通过Breadcrumb组件）
- **用途**: 提供页面层级导航信息
- **自动生成**: 基于URL路径自动生成面包屑导航

### 3. Organization Schema
- **位置**: 根布局（全站）
- **用途**: 描述网站组织信息
- **包含字段**: name, url, logo, description, contactPoint

### 4. WebSite Schema
- **位置**: 首页
- **用途**: 描述网站基本信息和搜索功能
- **包含字段**: name, url, description, potentialAction

### 5. CollectionPage Schema
- **位置**: 游戏分类页面
- **用途**: 描述游戏分类集合页面
- **包含字段**: name, description, mainEntity, breadcrumb

## 文件结构

```
lib/
├── structured-data.ts          # 结构化数据生成工具
components/
├── StructuredData.tsx          # 结构化数据渲染组件
├── └── Breadcrumb             # 面包屑导航组件
```

## 核心工具函数

### `generateVideoGameSchema(game, url)`
生成游戏的VideoGame结构化数据

### `generateBreadcrumbSchema(breadcrumbs)`
生成面包屑导航结构化数据

### `generateOrganizationSchema()`
生成网站组织结构化数据

### `generateWebSiteSchema()`
生成网站结构化数据

### `generateCollectionPageSchema(name, description, url, count)`
生成分类页面结构化数据

### `generateBreadcrumbsFromPath(pathname, gameTitle?)`
根据URL路径自动生成面包屑导航数据

## 使用方法

### 1. 在页面中添加结构化数据

```tsx
import StructuredData from '@/components/StructuredData';
import { generateVideoGameSchema } from '@/lib/structured-data';

// 在组件中
const gameSchema = generateVideoGameSchema(gameData, gameUrl);

return (
  <>
    <StructuredData data={gameSchema} />
    {/* 页面内容 */}
  </>
);
```

### 2. 添加面包屑导航

```tsx
import { Breadcrumb } from '@/components/StructuredData';
import { generateBreadcrumbsFromPath } from '@/lib/structured-data';

// 在组件中
const breadcrumbs = generateBreadcrumbsFromPath('/games/pokemon-games');

return (
  <Breadcrumb items={breadcrumbs} />
);
```

### 3. 多个结构化数据

```tsx
import { combineStructuredData } from '@/lib/structured-data';

const schemas = combineStructuredData(
  gameSchema,
  websiteSchema,
  organizationSchema
);

return <StructuredData data={schemas} />;
```

## 已实现页面

- ✅ 首页 (`/`) - VideoGame + WebSite + Organization
- ✅ 游戏详情页 (`/[game-slug]`) - VideoGame + Breadcrumb
- ✅ Pokemon游戏分类 (`/games/pokemon-games`) - CollectionPage + Breadcrumb
- ✅ RPG游戏分类 (`/games/rpg-games`) - CollectionPage + Breadcrumb
- ✅ 根布局 - Organization（全站）

## SEO优势

1. **丰富摘要**: 搜索结果中显示更多信息
2. **游戏信息**: 评分、价格、平台等信息
3. **面包屑导航**: 搜索结果中显示页面层级
4. **网站信息**: 提升品牌认知度
5. **分类信息**: 帮助搜索引擎理解内容分类

## 验证工具

可以使用以下工具验证结构化数据：
- [Google Rich Results Test](https://search.google.com/test/rich-results)
- [Schema.org Validator](https://validator.schema.org/)
- [JSON-LD Playground](https://json-ld.org/playground/)

## 最佳实践

1. **数据一致性**: 确保结构化数据与页面内容一致
2. **必需字段**: 确保所有必需字段都有有效值
3. **图片优化**: 使用高质量的图片URL
4. **URL规范**: 使用完整的绝对URL
5. **定期验证**: 定期检查结构化数据的有效性

## 扩展建议

1. **Article Schema**: 为博客文章添加Article结构化数据
2. **Review Schema**: 添加用户评论和评分
3. **FAQ Schema**: 为常见问题页面添加FAQ结构化数据
4. **Event Schema**: 如果有游戏活动，添加Event结构化数据

## 注意事项

- 结构化数据不会立即影响搜索排名，需要时间让搜索引擎索引
- 避免添加与页面内容不符的结构化数据
- 定期监控Google Search Console中的结构化数据报告
- 保持结构化数据的更新，特别是游戏信息变更时 