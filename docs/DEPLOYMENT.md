# Deployment Guide

This guide covers how to deploy the Gaming Platform to various hosting providers.

## 🚀 Vercel (Recommended)

Vercel is the recommended hosting platform for Next.js applications.

### Prerequisites
- GitHub account
- Vercel account (free tier available)

### Steps

1. **Push to GitHub**
   ```bash
   git add .
   git commit -m "Ready for deployment"
   git push origin main
   ```

2. **Connect to Vercel**
   - Go to [vercel.com](https://vercel.com)
   - Sign in with GitHub
   - Click "New Project"
   - Import your repository

3. **Configure Environment Variables**
   In Vercel dashboard, add these environment variables:
   ```
   NEXT_PUBLIC_BASE_URL=https://your-domain.vercel.app
   NEXT_PUBLIC_GA_TRACKING_ID=your-google-analytics-id
   ```

4. **Deploy**
   - Vercel will automatically build and deploy
   - Your site will be available at `https://your-project.vercel.app`

### Custom Domain
1. In Vercel dashboard, go to your project
2. Click "Domains" tab
3. Add your custom domain
4. Follow DNS configuration instructions

## 🌐 Netlify

### Steps

1. **Build the project**
   ```bash
   npm run build
   npm run export  # if using static export
   ```

2. **Deploy to Netlify**
   - Go to [netlify.com](https://netlify.com)
   - Drag and drop the `out` folder (for static export) or connect GitHub repo
   - Configure build settings:
     - Build command: `npm run build`
     - Publish directory: `.next` (or `out` for static export)

3. **Environment Variables**
   Add in Netlify dashboard under Site settings > Environment variables

## 🐳 Docker

### Dockerfile
```dockerfile
FROM node:18-alpine AS base

# Install dependencies only when needed
FROM base AS deps
RUN apk add --no-cache libc6-compat
WORKDIR /app

COPY package.json package-lock.json* ./
RUN npm ci

# Rebuild the source code only when needed
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .

RUN npm run build

# Production image, copy all the files and run next
FROM base AS runner
WORKDIR /app

ENV NODE_ENV production

RUN addgroup --system --gid 1001 nodejs
RUN adduser --system --uid 1001 nextjs

COPY --from=builder /app/public ./public

# Set the correct permission for prerender cache
RUN mkdir .next
RUN chown nextjs:nodejs .next

COPY --from=builder --chown=nextjs:nodejs /app/.next/standalone ./
COPY --from=builder --chown=nextjs:nodejs /app/.next/static ./.next/static

USER nextjs

EXPOSE 3000

ENV PORT 3000

CMD ["node", "server.js"]
```

### Build and Run
```bash
docker build -t gaming-platform .
docker run -p 3000:3000 gaming-platform
```

## ☁️ AWS (EC2 + PM2)

### Prerequisites
- AWS EC2 instance (Ubuntu/Amazon Linux)
- Node.js 18+ installed
- PM2 process manager

### Steps

1. **Connect to EC2**
   ```bash
   ssh -i your-key.pem ubuntu@your-ec2-ip
   ```

2. **Install dependencies**
   ```bash
   sudo apt update
   sudo apt install nodejs npm
   sudo npm install -g pm2
   ```

3. **Clone and setup**
   ```bash
   git clone your-repo-url
   cd pokemon-modele
   npm install
   npm run build
   ```

4. **Configure PM2**
   Create `ecosystem.config.js`:
   ```javascript
   module.exports = {
     apps: [{
       name: 'gaming-platform',
       script: 'npm',
       args: 'start',
       env: {
         NODE_ENV: 'production',
         PORT: 3000,
         NEXT_PUBLIC_BASE_URL: 'https://your-domain.com'
       }
     }]
   }
   ```

5. **Start with PM2**
   ```bash
   pm2 start ecosystem.config.js
   pm2 save
   pm2 startup
   ```

## 🔧 Environment Variables

### Required Variables
```env
NEXT_PUBLIC_BASE_URL=https://your-domain.com
```

### Optional Variables
```env
NEXT_PUBLIC_GA_TRACKING_ID=G-XXXXXXXXXX
NODE_ENV=production
```

## 📊 Performance Optimization

### Before Deployment
1. **Optimize Images**
   - Compress game thumbnails
   - Use WebP format when possible
   - Ensure proper sizing

2. **Bundle Analysis**
   ```bash
   npm install -g @next/bundle-analyzer
   ANALYZE=true npm run build
   ```

3. **Lighthouse Audit**
   - Test performance, accessibility, SEO
   - Fix any critical issues

### Post-Deployment
1. **CDN Setup**
   - Configure Vercel Edge Network (automatic)
   - Or setup CloudFlare for other providers

2. **Monitoring**
   - Setup Google Analytics
   - Monitor Core Web Vitals
   - Track error rates

## 🔒 Security Checklist

- [ ] Environment variables are secure
- [ ] No sensitive data in client-side code
- [ ] HTTPS enabled
- [ ] Security headers configured (done in next.config.mjs)
- [ ] Content Security Policy (optional)

## 🚨 Troubleshooting

### Common Issues

1. **Build Fails**
   - Check TypeScript errors: `npx tsc --noEmit`
   - Check ESLint errors: `npm run lint`
   - Verify all dependencies are installed

2. **Images Not Loading**
   - Check image paths in markdown files
   - Verify images exist in `public/images/thumbnails/`
   - Check Next.js image configuration

3. **404 on Refresh**
   - Ensure hosting provider supports SPA routing
   - Configure redirects for client-side routing

4. **Environment Variables Not Working**
   - Prefix client-side variables with `NEXT_PUBLIC_`
   - Restart development server after changes
   - Check deployment platform variable configuration

## 📞 Support

If you encounter deployment issues:
1. Check the [Next.js deployment documentation](https://nextjs.org/docs/deployment)
2. Review hosting provider specific guides
3. Open an issue on GitHub with deployment logs 