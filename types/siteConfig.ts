export interface SiteConfig {
  name: string;
  description: string;
  url: string;
  metadataBase: URL;
  keywords?: string[];
  authors: Array<{
    name: string;
    url: string;
    twitter?: string;
  }>;
  creator: string;
  themeColors: Array<{
    media: string;
    color: string;
  }>;
  defaultNextTheme: string;
  icons: {
    icon: string;
    shortcut: string;
    apple: string;
  };
  openGraph: {
    type: string;
    locale: string;
    url: string;
    title: string;
    description: string;
    siteName: string;
    images: string[];
  };
  twitter: {
    card: string;
    title: string;
    site: string;
    description: string;
    images: string[];
    creator: string;
  };
} 