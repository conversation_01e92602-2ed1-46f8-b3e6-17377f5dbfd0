{"name": "nextjs-i18n-starter", "version": "2.0.0", "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-toast": "^1.2.6", "@tailwindcss/typography": "^0.5.16", "@vercel/analytics": "^1.4.1", "@waline/client": "^3.5.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "gray-matter": "^4.0.3", "lucide-react": "^0.468.0", "next": "^15.3.3", "next-themes": "^0.4.4", "react": "^18.3.1", "react-dom": "^18.3.1", "react-icons": "^5.4.0", "remark": "^15.0.1", "remark-html": "^16.0.1", "tailwind-merge": "^2.5.5", "tailwindcss-animate": "^1.0.7", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/node": "^20", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "autoprefixer": "^10.4.19", "eslint": "^8", "eslint-config-next": "^15.3.3", "postcss": "^8.4.38", "tailwindcss": "^3.4.3", "typescript": "^5"}}