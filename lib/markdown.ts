import fs from 'fs';
import path from 'path';
import matter from 'gray-matter';
import { remark } from 'remark';
import html from 'remark-html';
import { GameFrontmatter, Game, HotGameConfig } from './types';

// Game files directory path
const gamesDirectory = path.join(process.cwd(), 'games');
const configDirectory = path.join(process.cwd(), 'config');

/**
 * Get all game slug list
 * Read all .md filenames (without extension) from games directory
 * Mainly used for generateStaticParams
 */
export function getGameSlugs(): string[] {
  try {
    // Ensure directory exists
    if (!fs.existsSync(gamesDirectory)) {
      return [];
    }

    const fileNames = fs.readdirSync(gamesDirectory);
    return fileNames
      .filter(fileName => fileName.endsWith('.md'))
      .map(fileName => fileName.replace(/\.md$/, ''));
  } catch (error) {
    return [];
  }
}

/**
 * Process markdown content to remove the first H1 heading and breadcrumb navigation
 * This allows the H1 to be displayed in the iframe player instead of the page content
 * Also removes manual breadcrumb navigation from markdown content
 */
export function removeFirstH1FromContent(content: string): string {
  // Split content into lines
  const lines = content.split('\n');
  let firstH1Removed = false;
  let skipNextBreadcrumb = false;
  
  const processedLines = lines.filter(line => {
    const trimmedLine = line.trim();
    
    // Check if this line is an H1 heading (starts with # but not ##)
    if (!firstH1Removed && trimmedLine.match(/^#\s+/)) {
      firstH1Removed = true;
      skipNextBreadcrumb = true; // Flag to skip potential breadcrumb on next lines
      return false; // Remove this line
    }
    
    // Skip breadcrumb navigation lines (usually after H1)
    // Pattern: [Text](url) / [Text](url) / Text
    if (skipNextBreadcrumb && trimmedLine.match(/^\[.*?\]\(.*?\)\s*\/.*$/)) {
      return false; // Remove breadcrumb line
    }
    
    // If we encounter non-empty, non-breadcrumb content after H1, stop skipping
    if (skipNextBreadcrumb && trimmedLine && !trimmedLine.match(/^\[.*?\]\(.*?\)\s*\/.*$/)) {
      skipNextBreadcrumb = false;
    }
    
    return true; // Keep all other lines
  });
  
  return processedLines.join('\n');
}

/**
 * Extract the first H1 heading from markdown content
 * This will be used as the display title in the iframe bar
 */
function extractFirstH1FromContent(content: string): string | null {
  const lines = content.split('\n');
  const firstH1Line = lines.find(line => line.trim().startsWith('# '));
  
  if (firstH1Line) {
    // Remove the '# ' prefix and return the title
    return firstH1Line.trim().substring(2).trim();
  }
  
  return null;
}

/**
 * Get complete game data by slug
 * Includes Frontmatter and rendered HTML content
 */
export async function getGameBySlug(slug: string): Promise<Game | null> {
  try {
    const fullPath = path.join(gamesDirectory, `${slug}.md`);
    
    // Check if file exists
    if (!fs.existsSync(fullPath)) {
      return null;
    }

    const fileContents = fs.readFileSync(fullPath, 'utf8');
    const { data, content } = matter(fileContents);

    // Validate required frontmatter fields
    const requiredFields = ['title', 'slug', 'thumbnail', 'genres', 'description', 'iframeUrl', 'publishedDate'];
    for (const field of requiredFields) {
      if (!data[field]) {
        return null;
      }
    }

    // Extract the first H1 from content for display title
    const displayTitle = extractFirstH1FromContent(content);

    // Remove the first H1 from content before processing
    const processedContent = removeFirstH1FromContent(content);

    // Process Markdown content using remark
    const remarkProcessed = await remark()
      .use(html)
      .process(processedContent);
    const contentHtml = remarkProcessed.toString();

    const game: Game = {
      ...(data as GameFrontmatter),
      contentHtml,
      displayTitle, // Add the extracted H1 title for display
    };

    return game;
  } catch (error) {
    return null;
  }
}

/**
 * Get all games' Frontmatter data with display titles
 * Supports sorting and genre filtering, includes displayTitle extracted from content
 */
export async function getAllGameFrontmatters(options?: {
  sortByDate?: boolean;
  filterByGenre?: string;
}): Promise<(GameFrontmatter & { displayTitle?: string | null })[]> {
  try {
    const slugs = getGameSlugs();
    const games: (GameFrontmatter & { displayTitle?: string | null })[] = [];

    for (const slug of slugs) {
      const fullPath = path.join(gamesDirectory, `${slug}.md`);
      
      try {
        const fileContents = fs.readFileSync(fullPath, 'utf8');
        const { data, content } = matter(fileContents);

        // Validate required fields
        const requiredFields = ['title', 'slug', 'thumbnail', 'genres', 'description', 'iframeUrl', 'publishedDate'];
        const isValid = requiredFields.every(field => data[field]);
        
        if (!isValid) {
          continue;
        }

        const frontmatter = data as GameFrontmatter;

        // Genre filtering
        if (options?.filterByGenre) {
          if (!frontmatter.genres.includes(options.filterByGenre)) {
            continue;
          }
        }

        // Extract display title from content
        const displayTitle = extractFirstH1FromContent(content);

        games.push({
          ...frontmatter,
          displayTitle
        });
      } catch (error) {
        continue;
      }
    }

    // Sort by date (newest first)
    if (options?.sortByDate) {
      games.sort((a, b) => {
        return new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime();
      });
    }

    return games;
  } catch (error) {
    return [];
  }
}

/**
 * Get hot games' Frontmatter data with display titles
 * Based on slug list and order from config/hot-games.json
 */
export async function getHotGameFrontmatters(): Promise<(GameFrontmatter & { displayTitle?: string | null })[]> {
  try {
    const hotGamesConfigPath = path.join(configDirectory, 'hot-games.json');
    
    // Check if config file exists
    if (!fs.existsSync(hotGamesConfigPath)) {
      return [];
    }

    const configContent = fs.readFileSync(hotGamesConfigPath, 'utf8');
    const hotGamesConfig: HotGameConfig[] = JSON.parse(configContent);

    const hotGames: (GameFrontmatter & { displayTitle?: string | null })[] = [];

    for (const config of hotGamesConfig) {
      const fullPath = path.join(gamesDirectory, `${config.slug}.md`);
      
      try {
        if (!fs.existsSync(fullPath)) {
          continue;
        }

        const fileContents = fs.readFileSync(fullPath, 'utf8');
        const { data, content } = matter(fileContents);

        // Validate required fields
        const requiredFields = ['title', 'slug', 'thumbnail', 'genres', 'description', 'iframeUrl', 'publishedDate'];
        const isValid = requiredFields.every(field => data[field]);
        
        if (!isValid) {
          continue;
        }

        // Extract display title from content
        const displayTitle = extractFirstH1FromContent(content);

        hotGames.push({
          ...(data as GameFrontmatter),
          displayTitle
        });
      } catch (error) {
        continue;
      }
    }

    return hotGames;
  } catch (error) {
    return [];
  }
}

/**
 * Get related games based on current game's genres
 * Returns games with similar genres, excluding the current game
 */
export async function getRelatedGameFrontmatters(
  currentGameSlug: string,
  limit: number = 8
): Promise<(GameFrontmatter & { displayTitle?: string | null })[]> {
  try {
    // Get current game to find its genres
    const currentGame = await getGameBySlug(currentGameSlug);
    if (!currentGame) {
      return [];
    }

    // Get all games
    const allGames = await getAllGameFrontmatters({ sortByDate: true });
    
    // Filter out current game and find related games
    const otherGames = allGames.filter(game => game.slug !== currentGameSlug);
    
    // Score games based on genre similarity
    const scoredGames = otherGames.map(game => {
      const commonGenres = game.genres.filter(genre => 
        currentGame.genres.includes(genre)
      );
      return {
        ...game,
        score: commonGenres.length
      };
    });

    // Sort by score (most similar first), then by date
    scoredGames.sort((a, b) => {
      if (a.score !== b.score) {
        return b.score - a.score; // Higher score first
      }
      return new Date(b.publishedDate).getTime() - new Date(a.publishedDate).getTime();
    });

    // Return limited results without score
    return scoredGames.slice(0, limit).map(({ score, ...game }) => game);
  } catch (error) {
    return [];
  }
} 