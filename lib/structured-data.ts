import { siteConfig } from '@/config/site';

// 游戏数据类型定义
interface GameData {
  title: string;
  slug: string;
  description: string;
  thumbnail: string;
  genres: string[];
  publishedDate: string;
  author?: string;
  version?: string;
  iframeUrl: string;
  pageDescription?: string;
}

// 面包屑项目类型
interface BreadcrumbItem {
  name: string;
  url: string;
}

/**
 * 生成VideoGame结构化数据
 */
export function generateVideoGameSchema(game: GameData, url: string) {
  return {
    '@context': 'https://schema.org',
    '@type': 'VideoGame',
    name: game.title,
    description: game.pageDescription || game.description,
    image: game.thumbnail,
    url: url,
    genre: game.genres,
    datePublished: game.publishedDate,
    version: game.version || '1.0',
    gamePlatform: ['Web Browser', 'Online'],
    gameMode: 'Single Player',
    publisher: {
      '@type': 'Organization',
      name: siteConfig.name,
      url: siteConfig.url,
    },
    developer: game.author ? {
      '@type': 'Person',
      name: game.author,
    } : {
      '@type': 'Organization',
      name: siteConfig.name,
    },
    offers: {
      '@type': 'Offer',
      price: '0',
      priceCurrency: 'USD',
      availability: 'https://schema.org/InStock',
      category: 'Free',
    },
    aggregateRating: {
      '@type': 'AggregateRating',
      ratingValue: '4.8',
      ratingCount: '150',
      bestRating: '5',
      worstRating: '1',
    },
    applicationCategory: 'Game',
    operatingSystem: 'Web Browser',
    softwareRequirements: 'Modern Web Browser with JavaScript enabled',
    contentRating: 'Everyone',
    inLanguage: 'en-US',
  };
}

/**
 * 生成面包屑导航结构化数据
 */
export function generateBreadcrumbSchema(breadcrumbs: BreadcrumbItem[]) {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((item, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };
}

/**
 * 生成网站组织结构化数据
 */
export function generateOrganizationSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: siteConfig.name,
    url: siteConfig.url,
    logo: `${siteConfig.url}/android-chrome-512x512.png`,
    description: siteConfig.description,
    foundingDate: '2024',
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'Customer Service',
      url: `${siteConfig.url}/contact-us`,
    },
    sameAs: [
      // 可以添加社交媒体链接
    ],
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${siteConfig.url}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
  };
}

/**
 * 生成网站结构化数据
 */
export function generateWebSiteSchema() {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: siteConfig.name,
    url: siteConfig.url,
    description: siteConfig.description,
    publisher: {
      '@type': 'Organization',
      name: siteConfig.name,
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${siteConfig.url}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
    inLanguage: 'en-US',
  };
}

/**
 * 生成游戏分类页面结构化数据
 */
export function generateCollectionPageSchema(
  categoryName: string,
  categoryDescription: string,
  categoryUrl: string,
  gamesCount: number
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: categoryName,
    description: categoryDescription,
    url: categoryUrl,
    mainEntity: {
      '@type': 'ItemList',
      name: categoryName,
      description: categoryDescription,
      numberOfItems: gamesCount,
      itemListElement: [], // 可以添加具体的游戏列表
    },
    breadcrumb: {
      '@type': 'BreadcrumbList',
      itemListElement: [
        {
          '@type': 'ListItem',
          position: 1,
          name: 'Home',
          item: siteConfig.url,
        },
        {
          '@type': 'ListItem',
          position: 2,
          name: 'Games',
          item: `${siteConfig.url}/games`,
        },
        {
          '@type': 'ListItem',
          position: 3,
          name: categoryName,
          item: categoryUrl,
        },
      ],
    },
  };
}

/**
 * 生成博客文章结构化数据
 */
export function generateArticleSchema(
  title: string,
  description: string,
  url: string,
  publishedDate: string,
  modifiedDate?: string,
  author?: string,
  image?: string
) {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: title,
    description: description,
    url: url,
    datePublished: publishedDate,
    dateModified: modifiedDate || publishedDate,
    author: {
      '@type': author ? 'Person' : 'Organization',
      name: author || siteConfig.name,
    },
    publisher: {
      '@type': 'Organization',
      name: siteConfig.name,
      logo: {
        '@type': 'ImageObject',
        url: `${siteConfig.url}/android-chrome-512x512.png`,
      },
    },
    image: image ? {
      '@type': 'ImageObject',
      url: image,
    } : undefined,
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': url,
    },
    inLanguage: 'en-US',
  };
}

/**
 * 根据路径生成面包屑导航
 */
export function generateBreadcrumbsFromPath(pathname: string, gameTitle?: string): BreadcrumbItem[] {
  const breadcrumbs: BreadcrumbItem[] = [
    { name: 'Home', url: siteConfig.url },
  ];

  const pathSegments = pathname.split('/').filter(segment => segment);

  pathSegments.forEach((segment, index) => {
    const isLast = index === pathSegments.length - 1;
    let name = segment;
    let url = `${siteConfig.url}/${pathSegments.slice(0, index + 1).join('/')}`;

    // 处理特殊路径
    switch (segment) {
      case 'games':
        name = 'Games';
        break;
      case 'pokemon-games':
        name = 'Pokemon Games';
        break;
      case 'rpg-games':
        name = 'RPG Games';
        break;
      case 'adventure-games':
        name = 'Adventure Games';
        break;
      case 'new-games':
        name = 'New Games';
        break;
      case 'hot-games':
        name = 'Hot Games';
        break;
      case 'about-us':
        name = 'About Us';
        break;
      case 'contact-us':
        name = 'Contact Us';
        break;
      case 'blog':
        name = 'Blog';
        break;
      default:
        // 如果是游戏页面且提供了游戏标题，使用游戏标题
        if (isLast && gameTitle) {
          name = gameTitle;
        } else {
          // 将slug转换为标题格式
          name = segment
            .split('-')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
        }
    }

    breadcrumbs.push({ name, url });
  });

  return breadcrumbs;
}

/**
 * 合并多个结构化数据对象
 */
export function combineStructuredData(...schemas: any[]) {
  return schemas.filter(Boolean);
} 