/**
 * Game Frontmatter interface definition
 * Corresponds to YAML front matter in Markdown files
 */
export interface GameFrontmatter {
  /** Game title */
  title: string;
  /** URL slug, corresponds to filename */
  slug: string;
  /** Thumbnail image path (e.g.: /images/thumbnails/game-slug.png) */
  thumbnail: string;
  /** Game genre tags array (e.g.: ["RPG", "Adventure"]) */
  genres: string[];
  /** Brief game description (for cards and meta info) */
  description: string;
  /** Detailed page description (for page display, optional) */
  pageDescription?: string;
  /** Game content iframe embed URL */
  iframeUrl: string;
  /** Published date (format: YYYY-MM-DD, for sorting) */
  publishedDate: string;
  /** Display title extracted from first H1 in markdown content */
  displayTitle?: string | null;
  
  // Optional meta information
  /** Author */
  author?: string;
  /** Version */
  version?: string;
}

/**
 * Complete game data structure
 * Extends GameFrontmatter with rendered HTML content and display title
 */
export interface Game extends GameFrontmatter {
  /** Markdown content converted to HTML string */
  contentHtml: string;
  /** Display title extracted from first H1 in markdown content */
  displayTitle?: string | null;
}

/**
 * Hot games configuration interface
 * Corresponds to config/hot-games.json data structure
 */
export interface HotGameConfig {
  slug: string;
} 