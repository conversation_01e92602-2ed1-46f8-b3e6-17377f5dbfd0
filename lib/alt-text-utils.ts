/**
 * Alt文本生成工具库
 * 为图片提供更精确、更符合SEO最佳实践的alt文本
 */

/**
 * 从游戏标题中提取核心游戏名
 * 移除冗长的描述性后缀，保留核心游戏名称
 */
export function extractGameName(title: string): string {
  if (!title) return 'Game';
  
  // 移除常见的描述性后缀
  const cleanTitle = title
    .replace(/[:|：]\s*.*$/, '') // 移除冒号后的所有内容
    .replace(/\s*-\s*.*$/, '') // 移除破折号后的所有内容
    .replace(/\s*\|\s*.*$/, '') // 移除竖线后的所有内容
    .replace(/\s*\(\s*.*\s*\)$/, '') // 移除括号内容
    .replace(/\s*\[\s*.*\s*\]$/, '') // 移除方括号内容
    .trim();
  
  return cleanTitle || title; // 如果清理后为空，返回原标题
}

/**
 * 从slug生成游戏名
 * 将连字符转换为空格并大写首字母
 */
export function slugToGameName(slug: string): string {
  if (!slug) return 'Game';
  
  return slug
    .split('-')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
}

/**
 * 生成游戏封面图片的Alt文本
 * 优先使用从title提取的游戏名，回退到slug转换
 */
export function generateGameCoverAlt(title?: string, slug?: string): string {
  let gameName: string;
  
  if (title) {
    gameName = extractGameName(title);
  } else if (slug) {
    gameName = slugToGameName(slug);
  } else {
    gameName = 'Game';
  }
  
  return `${gameName} game cover`;
}

/**
 * 生成游戏截图的Alt文本
 */
export function generateGameScreenshotAlt(title?: string, slug?: string): string {
  let gameName: string;
  
  if (title) {
    gameName = extractGameName(title);
  } else if (slug) {
    gameName = slugToGameName(slug);
  } else {
    gameName = 'Game';
  }
  
  return `${gameName} screenshot`;
}

/**
 * 生成游戏预览图的Alt文本
 */
export function generateGamePreviewAlt(title?: string, slug?: string): string {
  let gameName: string;
  
  if (title) {
    gameName = extractGameName(title);
  } else if (slug) {
    gameName = slugToGameName(slug);
  } else {
    gameName = 'Game';
  }
  
  return `${gameName} game preview`;
}

/**
 * 生成博客文章特色图片的Alt文本
 */
export function generateBlogImageAlt(title: string): string {
  if (!title) return 'Blog post image';
  
  // 移除常见的博客标题后缀
  const cleanTitle = title
    .replace(/\s*-\s*.*$/, '') // 移除破折号后的内容
    .replace(/[:|：]\s*.*$/, '') // 移除冒号后的内容
    .trim();
  
  return `${cleanTitle || title} blog post image`;
}

/**
 * 生成通用内容图片的Alt文本
 * 用于其他类型的内容图片
 */
export function generateContentImageAlt(title: string, contentType: string = 'content'): string {
  if (!title) return `${contentType} image`;
  
  const cleanTitle = extractGameName(title);
  return `${cleanTitle} ${contentType} image`;
}

/**
 * 验证Alt文本质量
 * 检查Alt文本是否符合SEO最佳实践
 */
export function validateAltText(altText: string): {
  isValid: boolean;
  issues: string[];
  suggestions: string[];
} {
  const issues: string[] = [];
  const suggestions: string[] = [];
  
  // 检查长度
  if (altText.length < 5) {
    issues.push('Alt text is too short');
    suggestions.push('Add more descriptive content');
  }
  
  if (altText.length > 125) {
    issues.push('Alt text is too long (over 125 characters)');
    suggestions.push('Shorten the description while keeping key information');
  }
  
  // 检查是否包含"image"、"picture"等冗余词汇
  const redundantWords = ['image of', 'picture of', 'photo of'];
  const hasRedundantWords = redundantWords.some(word => 
    altText.toLowerCase().includes(word)
  );
  
  if (hasRedundantWords) {
    issues.push('Contains redundant words like "image of" or "picture of"');
    suggestions.push('Remove redundant words - screen readers already announce it as an image');
  }
  
  // 检查是否只是文件名
  if (altText.includes('.jpg') || altText.includes('.png') || altText.includes('.gif')) {
    issues.push('Alt text appears to be a filename');
    suggestions.push('Use descriptive text instead of filename');
  }
  
  return {
    isValid: issues.length === 0,
    issues,
    suggestions
  };
}

/**
 * 测试函数 - 用于验证Alt文本生成逻辑
 */
export function testAltTextGeneration() {
  const testCases = [
    {
      title: "Pokémon Silver: Explore Johto & Kanto's Classic Adventure!",
      slug: "pokemon-silver",
      expected: "Pokémon Silver game cover"
    },
    {
      title: "Pokémon Gamma Emerald | Play online",
      slug: "pokemon-gamma-emerald", 
      expected: "Pokémon Gamma Emerald game cover"
    },
    {
      title: "PokeRogue: Your New Pokémon Roguelike Adventure!",
      slug: "pokerogue",
      expected: "PokeRogue game cover"
    }
  ];
  
  console.log('=== Alt Text Generation Tests ===');
  testCases.forEach(({ title, slug, expected }) => {
    const result = generateGameCoverAlt(title, slug);
    const passed = result === expected;
    console.log(`${passed ? '✅' : '❌'} ${title}`);
    console.log(`   Expected: ${expected}`);
    console.log(`   Got: ${result}`);
    if (!passed) {
      console.log(`   ❌ Test failed`);
    }
    console.log('');
  });
} 