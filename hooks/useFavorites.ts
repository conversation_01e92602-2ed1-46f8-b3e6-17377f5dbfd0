"use client";

import { useState, useEffect, useCallback } from 'react';
import { GameFrontmatter } from '@/lib/types';

const FAVORITES_STORAGE_KEY = 'pokemon-favorites';

export interface FavoriteGame {
  slug: string;
  title: string;
  thumbnail: string;
  genres: string[];
  description: string;
  addedAt: string; // ISO date string
}

export function useFavorites() {
  const [favorites, setFavorites] = useState<FavoriteGame[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // 从localStorage加载收藏列表
  useEffect(() => {
    try {
      const stored = localStorage.getItem(FAVORITES_STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        setFavorites(Array.isArray(parsed) ? parsed : []);
      }
    } catch (error) {
      console.error('Error loading favorites from localStorage:', error);
      setFavorites([]);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // 保存到localStorage
  const saveFavorites = useCallback((newFavorites: FavoriteGame[]) => {
    try {
      localStorage.setItem(FAVORITES_STORAGE_KEY, JSON.stringify(newFavorites));
      setFavorites(newFavorites);
    } catch (error) {
      console.error('Error saving favorites to localStorage:', error);
    }
  }, []);

  // 检查游戏是否已收藏
  const isFavorited = useCallback((slug: string) => {
    return favorites.some(fav => fav.slug === slug);
  }, [favorites]);

  // 添加到收藏
  const addToFavorites = useCallback((game: GameFrontmatter) => {
    if (isFavorited(game.slug)) {
      return; // 已经收藏了
    }

    const favoriteGame: FavoriteGame = {
      slug: game.slug,
      title: game.title,
      thumbnail: game.thumbnail,
      genres: game.genres,
      description: game.description,
      addedAt: new Date().toISOString(),
    };

    const newFavorites = [favoriteGame, ...favorites]; // 新收藏的放在前面
    saveFavorites(newFavorites);
  }, [favorites, isFavorited, saveFavorites]);

  // 从收藏中移除
  const removeFromFavorites = useCallback((slug: string) => {
    const newFavorites = favorites.filter(fav => fav.slug !== slug);
    saveFavorites(newFavorites);
  }, [favorites, saveFavorites]);

  // 切换收藏状态
  const toggleFavorite = useCallback((game: GameFrontmatter) => {
    if (isFavorited(game.slug)) {
      removeFromFavorites(game.slug);
      return false; // 返回新的收藏状态
    } else {
      addToFavorites(game);
      return true; // 返回新的收藏状态
    }
  }, [isFavorited, addToFavorites, removeFromFavorites]);

  // 清空所有收藏
  const clearAllFavorites = useCallback(() => {
    saveFavorites([]);
  }, [saveFavorites]);

  // 获取收藏数量
  const favoritesCount = favorites.length;

  return {
    favorites,
    isLoading,
    favoritesCount,
    isFavorited,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    clearAllFavorites,
  };
} 