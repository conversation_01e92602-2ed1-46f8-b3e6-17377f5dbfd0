/* Waline 评论系统自定义样式 */

/* 主面板样式 */
.wl-panel {
  @apply rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800;
  box-shadow: 0 1px 3px 0 rgb(0 0 0 / 0.1), 0 1px 2px -1px rgb(0 0 0 / 0.1);
}

/* 编辑器样式 */
.wl-editor {
  @apply rounded-md border-gray-300 dark:border-gray-600;
}

.wl-editor:focus {
  @apply border-blue-500 ring-1 ring-blue-500 dark:border-blue-400 dark:ring-blue-400;
}

/* 按钮样式 */
.wl-btn {
  @apply rounded-md transition-all duration-200;
}

.wl-btn-primary {
  @apply bg-blue-600 text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2;
}

.wl-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

/* 评论卡片样式 */
.wl-card {
  @apply rounded-lg border border-gray-100 bg-gray-50/50 dark:border-gray-700 dark:bg-gray-800/50;
  margin-bottom: 1rem;
}

.wl-card:hover {
  @apply border-gray-200 dark:border-gray-600;
  box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
}

/* 头像样式 */
.wl-avatar {
  @apply rounded-full;
  border: 2px solid #e5e7eb;
}

.dark .wl-avatar {
  border-color: #374151;
}

/* 用户信息样式 */
.wl-nick {
  @apply font-medium text-gray-900 dark:text-white;
}

.wl-time {
  @apply text-sm text-gray-500 dark:text-gray-400;
}

/* 评论内容样式 */
.wl-content {
  @apply text-gray-700 dark:text-gray-300 leading-relaxed;
}

.wl-content p {
  @apply mb-2;
}

.wl-content a {
  @apply text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300;
}

.wl-content code {
  @apply bg-gray-100 dark:bg-gray-700 px-1 py-0.5 rounded text-sm font-mono;
}

.wl-content pre {
  @apply bg-gray-100 dark:bg-gray-700 p-3 rounded-md overflow-x-auto;
}

.wl-content blockquote {
  @apply border-l-4 border-blue-500 pl-4 py-2 my-4 bg-blue-50 dark:bg-blue-900/20 italic;
}

/* 操作按钮样式 */
.wl-action {
  @apply text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400;
}

/* 点赞按钮样式 */
.wl-like {
  @apply text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400;
}

.wl-like.active {
  @apply text-red-500 dark:text-red-400;
}

/* 回复按钮样式 */
.wl-reply {
  @apply text-gray-500 hover:text-blue-600 dark:text-gray-400 dark:hover:text-blue-400;
}

/* 表情选择器样式 */
.wl-emoji-container {
  @apply rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800;
  box-shadow: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);
}

.wl-emoji-item {
  @apply hover:bg-gray-100 dark:hover:bg-gray-700 rounded;
  transition: background-color 0.15s ease-in-out;
}

/* 分页样式 */
.wl-pagination {
  @apply flex justify-center space-x-2 mt-6;
}

.wl-pagination .wl-page-number {
  @apply px-3 py-2 text-sm border border-gray-300 rounded-md hover:bg-gray-50 dark:border-gray-600 dark:hover:bg-gray-700;
}

.wl-pagination .wl-page-number.current {
  @apply bg-blue-600 text-white border-blue-600;
}

/* 加载状态样式 */
.wl-loading {
  @apply flex justify-center items-center py-8;
}

.wl-loading::after {
  content: '';
  @apply w-6 h-6 border-2 border-blue-600 border-t-transparent rounded-full animate-spin;
}

/* 空状态样式 */
.wl-empty {
  @apply text-center py-12 text-gray-500 dark:text-gray-400;
}

/* 输入框样式 */
.wl-input {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-400;
}

/* 文本域样式 */
.wl-textarea {
  @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:border-blue-500 focus:ring-1 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:text-white dark:focus:border-blue-400;
  min-height: 100px;
  resize: vertical;
}

/* 错误提示样式 */
.wl-error {
  @apply text-red-600 dark:text-red-400 text-sm mt-1;
}

/* 成功提示样式 */
.wl-success {
  @apply text-green-600 dark:text-green-400 text-sm mt-1;
}

/* 管理员标识样式 */
.wl-admin {
  @apply bg-gradient-to-r from-purple-500 to-pink-500 text-white px-2 py-1 rounded-full text-xs font-medium;
}

/* 置顶评论样式 */
.wl-sticky {
  @apply border-l-4 border-yellow-500 bg-yellow-50 dark:bg-yellow-900/20;
}

/* 响应式调整 */
@media (max-width: 640px) {
  .wl-panel {
    @apply mx-2;
  }
  
  .wl-card {
    @apply mx-0;
  }
  
  .wl-avatar {
    width: 32px;
    height: 32px;
  }
}

/* 动画效果 */
.wl-card {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 评论数统计样式 */
.wl-count {
  @apply text-sm text-gray-600 dark:text-gray-400 font-medium;
}

/* 排序选择器样式 */
.wl-sort {
  @apply text-sm text-gray-600 dark:text-gray-400 hover:text-blue-600 dark:hover:text-blue-400;
}

/* 预览模式样式 */
.wl-preview {
  @apply border border-gray-200 rounded-md p-3 bg-gray-50 dark:border-gray-700 dark:bg-gray-800;
}

/* 自定义滚动条 */
.wl-content::-webkit-scrollbar {
  width: 6px;
}

.wl-content::-webkit-scrollbar-track {
  @apply bg-gray-100 dark:bg-gray-700;
}

.wl-content::-webkit-scrollbar-thumb {
  @apply bg-gray-400 dark:bg-gray-500 rounded-full;
}

.wl-content::-webkit-scrollbar-thumb:hover {
  @apply bg-gray-500 dark:bg-gray-400;
} 