@tailwind base;
@tailwind components;
@tailwind utilities;

/* Game content grid layout */
.game-content-grid {
  display: grid;
  gap: 1rem;
  grid-template-areas: "main-player";
  grid-template-columns: 1fr;
  align-items: start;
}

/* Desktop layout (≥1200px) */
@media (min-width: 1200px) {
  .game-content-grid {
    grid-template-areas: "left-games main-player right-games";
    grid-template-columns: 280px 1fr 280px;
    gap: 1.5rem;
  }
}

/* Tablet layout (768px - 1199px) */
@media (min-width: 768px) and (max-width: 1199px) {
  .game-content-grid {
    grid-template-areas: "left-games main-player right-games";
    grid-template-columns: 280px 1fr 280px;
    gap: 1.25rem;
  }
}

/* Grid area assignments */
.left-games {
  grid-area: left-games;
  position: relative;
  height: fit-content;
}

.main-player {
  grid-area: main-player;
  position: relative;
  z-index: 1;
}

.right-games {
  grid-area: right-games;
  position: relative;
  height: fit-content;
}

/* Add subtle background gradient for better visual separation */
.game-content-grid::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.02) 0%, rgba(147, 51, 234, 0.02) 100%);
  border-radius: 1rem;
  pointer-events: none;
  z-index: -1;
}

/* Vertical game cards container */
.vertical-games-container {
  background: white;
  border-radius: 0.75rem;
  padding: 1rem;
  border: 1px solid rgba(229, 231, 235, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.3s ease;
}

.dark .vertical-games-container {
  background: rgba(31, 41, 55, 1);
  border-color: rgba(55, 65, 81, 1);
}

.vertical-games-container:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* Responsive improvements */
@media (max-width: 767px) {
  .game-content-grid {
    padding: 0.5rem;
  }
}

html {
  scroll-behavior: smooth;
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;

    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --chart-1: 12 76% 61%;

    --chart-2: 173 58% 39%;

    --chart-3: 197 37% 24%;

    --chart-4: 43 74% 66%;

    --chart-5: 27 87% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* 移动端优化样式 */
@layer utilities {
  /* 防止移动端水平滚动 */
  .prevent-horizontal-scroll {
    overflow-x: hidden;
    max-width: 100vw;
  }

  /* 移动端触摸优化 */
  .touch-action-pan-y {
    touch-action: pan-y;
  }

  /* 移动端侧边栏动画优化 */
  .sidebar-mobile {
    transform: translateX(-100%);
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sidebar-mobile.open {
    transform: translateX(0);
  }

  /* 移动端遮罩层渐变效果 */
  .mobile-overlay {
    opacity: 0;
    transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .mobile-overlay.visible {
    opacity: 1;
  }

  /* 移动端安全区域适配 */
  .safe-area-top {
    padding-top: env(safe-area-inset-top);
  }

  .safe-area-bottom {
    padding-bottom: env(safe-area-inset-bottom);
  }

  .safe-area-left {
    padding-left: env(safe-area-inset-left);
  }

  .safe-area-right {
    padding-right: env(safe-area-inset-right);
  }

  /* 移动端点击高亮优化 */
  .tap-highlight-transparent {
    -webkit-tap-highlight-color: transparent;
  }

  /* 移动端滚动优化 */
  .smooth-scroll {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  /* 移动端字体大小优化 */
  @media (max-width: 640px) {
    .text-responsive-sm {
      font-size: 0.875rem;
      line-height: 1.25rem;
    }
    
    .text-responsive-base {
      font-size: 1rem;
      line-height: 1.5rem;
    }
    
    .text-responsive-lg {
      font-size: 1.125rem;
      line-height: 1.75rem;
    }
  }

  /* 移动端按钮优化 */
  @media (max-width: 640px) {
    .btn-mobile {
      min-height: 44px;
      min-width: 44px;
      padding: 0.75rem 1rem;
    }
  }

  /* 移动端卡片间距优化 */
  @media (max-width: 640px) {
    .card-mobile {
      margin: 0.5rem;
      padding: 1rem;
    }
  }
}