import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Sidebar UI Test - Pokemon Gamma Emerald",
  description: "Testing the improved sidebar user interface",
};

export default function TestPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-8 text-gray-900 dark:text-white">
          Sidebar UI Improvements
        </h1>
        
        <div className="space-y-6">
          <div className="bg-white dark:bg-gray-800 rounded-lg p-6 shadow-lg border border-gray-200 dark:border-gray-700">
            <h2 className="text-2xl font-semibold mb-4 text-gray-900 dark:text-white">
              What's New?
            </h2>
            <ul className="space-y-3 text-gray-700 dark:text-gray-300">
              <li className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-2 h-2 bg-blue-500 rounded-full mt-2"></span>
                <span>
                  <strong>Clear Toggle Button:</strong> The sidebar toggle button is now prominently displayed in the header navigation bar for both desktop and mobile users.
                </span>
              </li>
              <li className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-2 h-2 bg-green-500 rounded-full mt-2"></span>
                <span>
                  <strong>Intuitive Icons:</strong> Desktop users see panel icons (open/close) while mobile users see the familiar hamburger menu icon.
                </span>
              </li>
              <li className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-2 h-2 bg-purple-500 rounded-full mt-2"></span>
                <span>
                  <strong>Better Accessibility:</strong> Enhanced tooltips and ARIA labels for screen readers and better user guidance.
                </span>
              </li>
              <li className="flex items-start space-x-3">
                <span className="flex-shrink-0 w-2 h-2 bg-orange-500 rounded-full mt-2"></span>
                <span>
                  <strong>Consistent Experience:</strong> Removed the hidden edge toggle button that was hard to discover.
                </span>
              </li>
            </ul>
          </div>

          <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6 border border-blue-200 dark:border-blue-800">
            <h2 className="text-2xl font-semibold mb-4 text-blue-900 dark:text-blue-100">
              How to Use
            </h2>
            <div className="space-y-4 text-blue-800 dark:text-blue-200">
              <div>
                <h3 className="font-semibold mb-2">Desktop Users:</h3>
                <p>Look for the panel icon in the top-left corner of the header. Click it to toggle the sidebar open/closed.</p>
              </div>
              <div>
                <h3 className="font-semibold mb-2">Mobile Users:</h3>
                <p>Tap the hamburger menu in the top-left corner to open the sidebar. Tap the X button or tap outside to close it.</p>
              </div>
            </div>
          </div>

          <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6 border border-green-200 dark:border-green-800">
            <h2 className="text-2xl font-semibold mb-4 text-green-900 dark:text-green-100">
              Try It Now!
            </h2>
            <p className="text-green-800 dark:text-green-200 mb-4">
              Test the new sidebar functionality by clicking the toggle button in the header navigation bar. 
              Notice how the icon changes based on the sidebar state and device type.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-2">
                <h4 className="font-semibold text-green-900 dark:text-green-100">Desktop Features:</h4>
                <ul className="space-y-1 text-green-700 dark:text-green-300">
                  <li>• Visual state indicator (open/close icons)</li>
                  <li>• Hover effects for better feedback</li>
                  <li>• Smooth transitions</li>
                  <li>• Content adapts to sidebar width</li>
                </ul>
              </div>
              <div className="space-y-2">
                <h4 className="font-semibold text-green-900 dark:text-green-100">Mobile Features:</h4>
                <ul className="space-y-1 text-green-700 dark:text-green-300">
                  <li>• Full-screen overlay</li>
                  <li>• Swipe gestures support</li>
                  <li>• Tap outside to close</li>
                  <li>• Scrolling prevention when open</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
} 