import type { MetadataRoute } from 'next'

const siteUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://pokemon-gamma-emerald.com'

export default function robots(): MetadataRoute.Robots {
  return {
    rules: {
      userAgent: '*',
      allow: '/',
      // 最终确认需要禁止的只有这三个目录
      disallow: [
        '/private/', // 禁止访问私人内容
        '/api/',     // 禁止访问API路由
        '/_next/',   // 禁止访问Next.js内部文件
      ],
    },
    // 保留指向sitemap的正确指令
    sitemap: `${siteUrl}/sitemap.xml`,
  }
}