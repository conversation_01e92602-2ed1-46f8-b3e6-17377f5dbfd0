import GameCard from '@/components/GameCard';
import StructuredData from '@/components/StructuredData';
import { Breadcrumb } from '@/components/StructuredData';
import { getAllGameFrontmatters } from '@/lib/markdown';
import { generateCollectionPageSchema, generateBreadcrumbsFromPath } from '@/lib/structured-data';
import { siteConfig } from '@/config/site';
import { Metadata } from 'next';
import Link from 'next/link';

// Generate static metadata for SEO
export const metadata: Metadata = {
  title: 'Pokemon Games',
  description: 'Discover amazing Pokemon games on our platform. Play the best Pokemon games online for free, catch, train, and battle with Pokemon creatures.',
};

export default async function PokemonGamesPage() {
  // Get games filtered by Pokemon games genre and sorted by date
  const games = await getAllGameFrontmatters({ 
    filterByGenre: 'Pokemon games', 
    sortByDate: true 
  });

  // 生成结构化数据
  const categoryUrl = `${siteConfig.url}/games/pokemon-games`;
  const collectionSchema = generateCollectionPageSchema(
    'Pokemon Games',
    'Discover amazing Pokemon games on our platform. Play the best Pokemon games online for free, catch, train, and battle with Pokemon creatures.',
    categoryUrl,
    games.length
  );

  // 生成面包屑导航
  const breadcrumbs = generateBreadcrumbsFromPath('/games/pokemon-games');

  return (
    <>
      {/* 结构化数据 */}
      <StructuredData data={collectionSchema} />
      
      <div className="min-h-screen bg-white dark:bg-gray-900">
        {/* 面包屑导航 */}
        <div className="container mx-auto px-4 pt-4">
          <Breadcrumb items={breadcrumbs} className="mb-4" />
        </div>
        
        {/* Header Section */}
      <section className="bg-gradient-to-r from-yellow-500 to-red-500 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Pokemon Games
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto">
            Catch, train, and battle with Pokemon creatures in these amazing adventures!
          </p>
        </div>
      </section>

      {/* Games Grid Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          {games.length > 0 ? (
            <>
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Pokemon Games ({games.length})
                </h2>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {games.map((game) => (
                  <GameCard key={game.slug} game={game} />
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">⚡</div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                No Pokemon Games Found
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-8">
                We&apos;re working on adding more Pokemon games. Check back soon!
              </p>
              <Link
                href="/new-games"
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors inline-block"
              >
                Browse All Games
              </Link>
            </div>
          )}
        </div>
      </section>
      </div>
    </>
  );
} 