import GameCard from '@/components/GameCard';
import { getAllGameFrontmatters } from '@/lib/markdown';
import { Metadata } from 'next';
import Link from 'next/link';

// Generate static metadata for SEO
export const metadata: Metadata = {
  title: 'Adventure Games',
  description: 'Discover amazing adventure games on our platform. Play the best adventure games online for free, explore captivating worlds and uncover mysteries.',
};

export default async function AdventureGamesPage() {
  // Get games filtered by Adventure games genre and sorted by date
  const games = await getAllGameFrontmatters({ 
    filterByGenre: 'Adventure games', 
    sortByDate: true 
  });

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Header Section */}
      <section className="bg-gradient-to-r from-green-600 to-teal-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            Adventure Games
          </h1>
          <p className="text-xl text-white/80 max-w-2xl mx-auto">
            Explore captivating worlds, solve mysteries, and embark on unforgettable journeys!
          </p>
        </div>
      </section>

      {/* Games Grid Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          {games.length > 0 ? (
            <>
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Adventure Games ({games.length})
                </h2>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {games.map((game) => (
                  <GameCard key={game.slug} game={game} />
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🗺️</div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                No Adventure Games Found
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-8">
                We&apos;re working on adding more adventure games. Check back soon!
              </p>
              <Link
                href="/new-games"
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors inline-block"
              >
                Browse All Games
              </Link>
            </div>
          )}
        </div>
      </section>
    </div>
  );
} 