"use client";

import { Metadata } from 'next';
import Link from 'next/link';
import { Heart, Trash2, ExternalLink } from 'lucide-react';
import { useFavorites } from '@/hooks/useFavorites';
import GameCard from '@/components/GameCard';
import { useState } from 'react';

// 注意：由于这是客户端组件，metadata需要在父组件或layout中设置
// export const metadata: Metadata = {
//   title: 'My Favorites - Pokemon Gamma Emerald',
//   description: 'Your favorite Pokemon games collection. Save and organize your most loved games.',
//   keywords: ['favorites', 'saved games', 'bookmarks', 'pokemon games', 'gaming collection'],
// };

export default function FavoritesPage() {
  const { favorites, isLoading, favoritesCount, removeFromFavorites, clearAllFavorites } = useFavorites();
  const [showClearConfirm, setShowClearConfirm] = useState(false);

  // 处理清空所有收藏
  const handleClearAll = () => {
    if (showClearConfirm) {
      clearAllFavorites();
      setShowClearConfirm(false);
    } else {
      setShowClearConfirm(true);
      // 3秒后自动取消确认状态
      setTimeout(() => setShowClearConfirm(false), 3000);
    }
  };

  // 处理移除单个收藏
  const handleRemoveFavorite = (slug: string) => {
    removeFromFavorites(slug);
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Heart className="w-12 h-12 text-red-500 mx-auto mb-4 animate-pulse" />
          <p className="text-gray-600 dark:text-gray-300">Loading your favorites...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* 页面标题 */}
        <div className="text-center mb-8">
          <div className="flex items-center justify-center mb-4">
            <Heart className="w-10 h-10 text-red-500 mr-3" />
            <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
              My Favorites
            </h1>
          </div>
          <p className="text-lg text-gray-600 dark:text-gray-300">
            {favoritesCount > 0 
              ? `You have ${favoritesCount} favorite game${favoritesCount > 1 ? 's' : ''}`
              : 'Start building your favorite games collection'
            }
          </p>
        </div>

        {/* 有收藏游戏时显示 */}
        {favoritesCount > 0 ? (
          <>
            {/* 操作栏 */}
            <div className="flex justify-between items-center mb-6">
              <div className="text-sm text-gray-500 dark:text-gray-400">
                Sorted by most recently added
              </div>
              <button
                onClick={handleClearAll}
                className={`flex items-center gap-2 px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                  showClearConfirm
                    ? 'bg-red-600 hover:bg-red-700 text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-300 hover:bg-gray-300 dark:hover:bg-gray-600'
                }`}
              >
                <Trash2 className="w-4 h-4" />
                {showClearConfirm ? 'Click again to confirm' : 'Clear All'}
              </button>
          </div>

            {/* 收藏游戏网格 */}
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-6">
              {favorites.map((game) => (
                <div key={game.slug} className="relative group">
                  {/* 游戏卡片 */}
                  <GameCard
                    game={{
                      title: game.title,
                      slug: game.slug,
                      thumbnail: game.thumbnail,
                      genres: game.genres,
                      description: game.description,
                      iframeUrl: '', // GameCard不需要这个字段
                      publishedDate: game.addedAt.split('T')[0] // 转换为YYYY-MM-DD格式
                    }}
                  />
                  
                  {/* 移除按钮 */}
                  <button
                    onClick={() => handleRemoveFavorite(game.slug)}
                    className="absolute top-2 right-2 p-2 bg-red-500 hover:bg-red-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity shadow-lg"
                    title="Remove from favorites"
                    aria-label="Remove from favorites"
                  >
                    <Heart className="w-4 h-4 fill-current" />
                  </button>

                  {/* 快速访问按钮 */}
                  <Link
                    href={`/${game.slug}`}
                    className="absolute bottom-2 right-2 p-2 bg-blue-500 hover:bg-blue-600 text-white rounded-full opacity-0 group-hover:opacity-100 transition-opacity shadow-lg"
                    title="Play game"
                    aria-label="Play game"
                  >
                    <ExternalLink className="w-4 h-4" />
                  </Link>
          </div>
              ))}
        </div>
          </>
        ) : (
          /* 空状态 */
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-12 text-center max-w-2xl mx-auto">
          <Heart className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-6" />
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white mb-4">
            No Favorites Yet
          </h2>
            <p className="text-gray-600 dark:text-gray-300 mb-8">
              Click the ❤️ button on any game to add it to your favorites. 
            Your saved games will appear here for quick access.
          </p>
          
          {/* 快速导航按钮 */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/new-games"
              className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors inline-block"
            >
              Browse New Games
            </Link>
            <Link
              href="/hot-games"
              className="bg-green-600 hover:bg-green-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors inline-block"
            >
              View Hot Games
            </Link>
            <Link
              href="/games/pokemon-games"
              className="bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors inline-block"
            >
              Explore Pokemon Games
            </Link>
          </div>
        </div>
        )}

        {/* 使用提示 */}
        {favoritesCount > 0 && (
          <div className="mt-12 text-center">
            <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6 max-w-2xl mx-auto">
              <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-200 mb-2">
                💡 Pro Tips
              </h3>
              <ul className="text-sm text-blue-800 dark:text-blue-300 space-y-1">
                <li>• Hover over games to see quick action buttons</li>
                <li>• Your favorites are saved locally in your browser</li>
                <li>• Click the heart button again to remove from favorites</li>
              </ul>
            </div>
        </div>
        )}
      </div>
    </div>
  );
} 