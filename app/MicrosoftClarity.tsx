"use client";

import Script from "next/script";

// Microsoft Clarity 项目ID
const CLARITY_PROJECT_ID: string | null = process.env.NEXT_PUBLIC_CLARITY_ID || null;

const MicrosoftClarity = () => {
  return (
    <>
      {CLARITY_PROJECT_ID ? (
        <Script
          id="microsoft-clarity-init"
          strategy="afterInteractive"
          dangerouslySetInnerHTML={{
            __html: `
              (function(c,l,a,r,i,t,y){
                c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
                t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
                y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
              })(window, document, "clarity", "script", "${CLARITY_PROJECT_ID}");
            `,
          }}
        />
      ) : null}
    </>
  );
};

export default MicrosoftClarity; 