import GoogleAnalytics from "@/app/GoogleAnalytics";
import MicrosoftClarity from "@/app/MicrosoftClarity";
import Footer from "@/components/footer/Footer";
import Header from "@/components/header/Header";
import Sidebar, { SidebarProvider } from "@/components/Sidebar";
import MainContent from "@/components/MainContent";
import ErrorBoundary from "@/components/ErrorBoundary";
import StructuredData from "@/components/StructuredData";
import { siteConfig } from "@/config/site";
import { generateOrganizationSchema } from "@/lib/structured-data";
import { cn } from "@/lib/utils";
import "@/styles/globals.css";
import "@/styles/loading.css";
import { Analytics } from "@vercel/analytics/react";
import { Viewport } from "next";
import { ThemeProvider } from "next-themes";

export const metadata = {
  title: siteConfig.name,
  description: siteConfig.description,
  authors: siteConfig.authors,
  creator: siteConfig.creator,
  icons: siteConfig.icons,
  metadataBase: siteConfig.metadataBase,
  openGraph: siteConfig.openGraph,
  twitter: siteConfig.twitter,
};
export const viewport: Viewport = {
  themeColor: siteConfig.themeColors,
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  // 生成网站级别的结构化数据
  const organizationSchema = generateOrganizationSchema();

  return (
    <html lang="en" suppressHydrationWarning>
      <head />
      <body className={cn("min-h-screen bg-background font-sans antialiased")}>
        {/* 网站级别结构化数据 */}
        <StructuredData data={organizationSchema} />
        
        <ThemeProvider
          attribute="class"
          defaultTheme={siteConfig.defaultNextTheme}
          enableSystem
          disableTransitionOnChange
        >
          <SidebarProvider>
            <ErrorBoundary>
              <Header />
              <Sidebar />
              <MainContent>
                {children}
              </MainContent>
              <Footer />
            </ErrorBoundary>
          </SidebarProvider>
          <Analytics />
        </ThemeProvider>
        {process.env.NODE_ENV === "development" ? (
          <></>
        ) : (
          <>
            <GoogleAnalytics />
            <MicrosoftClarity />
          </>
        )}
      </body>
    </html>
  );
}
