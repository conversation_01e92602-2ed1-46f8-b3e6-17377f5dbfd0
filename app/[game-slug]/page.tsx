import GamePlayer from '@/components/GamePlayer';
import GameContentSection from '@/components/GameContentSection';
import RelatedGamesCarousel from '@/components/RelatedGamesCarousel';
import StaticMessageBoard from '@/components/StaticMessageBoard';
import StructuredData from '@/components/StructuredData';
import VerticalGameCards from '@/components/VerticalGameCards';
import { Breadcrumb } from '@/components/StructuredData';
import { getGameBySlug, getGameSlugs, getRelatedGameFrontmatters, getAllGameFrontmatters, getHotGameFrontmatters } from '@/lib/markdown';
import { generateVideoGameSchema, generateBreadcrumbsFromPath, combineStructuredData } from '@/lib/structured-data';
import { siteConfig } from '@/config/site';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';

interface GamePageProps {
  params: Promise<{
    'game-slug': string;
  }>;
}

// Generate static params for all games
export async function generateStaticParams() {
  const slugs = getGameSlugs();
  
  return slugs.map((slug) => ({
    'game-slug': slug,
  }));
}

// Generate dynamic metadata for SEO
export async function generateMetadata({ params }: GamePageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const gameSlug = resolvedParams['game-slug'];
  const game = await getGameBySlug(gameSlug);
  
  if (!game) {
    return {
      title: 'Game Not Found',
      description: 'The requested game could not be found.',
    };
  }

  return {
    title: game.title,
    description: game.description,
    authors: game.author ? [{ name: game.author }] : undefined,
    openGraph: {
      title: game.title,
      description: game.description,
      type: 'website',
      url: `/${gameSlug}`,
      images: [
        {
          url: game.thumbnail,
          width: 1200,
          height: 630,
          alt: game.title,
        },
      ],
      siteName: 'pokemon-gamma-emerald.com',
    },
    twitter: {
      card: 'summary_large_image',
      title: game.title,
      description: game.description,
      images: [game.thumbnail],
    },
    alternates: {
      canonical: `/${gameSlug}`,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default async function GamePage({ params }: GamePageProps) {
  const resolvedParams = await params;
  const gameSlug = resolvedParams['game-slug'];
  const game = await getGameBySlug(gameSlug);
  
  // If game is not found, show 404 page
  if (!game) {
    notFound();
  }

  // 获取相关数据
  const [relatedGames, newGames, hotGames] = await Promise.all([
    getRelatedGameFrontmatters(gameSlug, 8),
    getAllGameFrontmatters({ sortByDate: true }).then(games => games.slice(0, 6)),
    getHotGameFrontmatters().then(games => games.slice(0, 6))
  ]);

  // 生成结构化数据
  const gameUrl = `${siteConfig.url}/${gameSlug}`;
  const gameSchema = generateVideoGameSchema({
    title: game.title,
    slug: game.slug,
    description: game.description,
    thumbnail: game.thumbnail,
    genres: game.genres,
    publishedDate: game.publishedDate,
    author: game.author,
    version: game.version,
    iframeUrl: game.iframeUrl,
    pageDescription: game.pageDescription,
  }, gameUrl);

  // 生成面包屑导航
  const breadcrumbs = generateBreadcrumbsFromPath(`/${gameSlug}`, game.title);

  return (
    <>
      {/* 结构化数据 */}
      <StructuredData data={gameSchema} />
      
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* 面包屑导航 */}
        <div className="w-full px-2 sm:px-4 lg:px-6 pt-4 pb-2">
          <Breadcrumb items={breadcrumbs} className="mb-2" />
        </div>
        
        {/* 主内容区域 - 全宽布局，响应侧边栏状态 */}
        <div className="w-full px-2 sm:px-4 lg:px-6 py-4">
        {/* Main game area with responsive layout */}
        <div className="game-content-grid mb-6">
          {/* Left game cards - Hidden on mobile */}
          <div className="left-games hidden md:block">
            <VerticalGameCards
              games={newGames}
              title="Latest Games"
              maxItems={4}
            />
          </div>

          {/* Main game player - Expanded width */}
          <div className="main-player">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 lg:p-8 transition-shadow duration-300 hover:shadow-xl">
              <GamePlayer
                title={game.title}
                displayTitle={game.displayTitle}
                thumbnail={game.thumbnail}
                iframeUrl={game.iframeUrl}
                gameSlug={game.slug}
                gameData={{
                  title: game.title,
                  slug: game.slug,
                  thumbnail: game.thumbnail,
                  genres: game.genres,
                  description: game.description,
                  iframeUrl: game.iframeUrl,
                  publishedDate: game.publishedDate,
                  author: game.author,
                  version: game.version,
                  pageDescription: game.pageDescription
                }}
                className="w-full"
              />
            </div>
          </div>

          {/* Right game cards - Hidden on mobile */}
          <div className="right-games hidden md:block">
            <VerticalGameCards
              games={hotGames}
              title="Hot Games"
              maxItems={4}
            />
          </div>
        </div>

        {/* Bottom carousel section */}
        <div className="mb-6">
          <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 lg:p-8 transition-shadow duration-300 hover:shadow-xl">
            <RelatedGamesCarousel
              games={relatedGames}
              title="Related Games"
              autoPlay={true}
              autoPlayInterval={4000}
              cardSize="medium"
              showTags={true}
            />
          </div>
        </div>

        {/* 底部区域：左侧内容说明 + 右侧留言板 */}
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-3 lg:gap-4">
          {/* 左侧游戏内容说明描述区 */}
          <div className="lg:col-span-8">
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
              <GameContentSection 
                game={game}
                showGameMeta={true}
                className="p-0" // 移除默认padding，因为容器已有
                contentClassName="prose prose-sm lg:prose-lg prose-gray dark:prose-invert max-w-none"
              />
            </div>
          </div>

          {/* 右侧静态留言板 */}
          <div className="lg:col-span-4">
            <StaticMessageBoard 
              gameSlug={game.slug}
              className="h-fit"
              showNotice={true}
            />
          </div>
        </div>
        </div>
      </div>
    </>
  );
} 