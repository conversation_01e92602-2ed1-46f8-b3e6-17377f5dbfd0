import GameCard from '@/components/GameCard';
import { getHotGameFrontmatters } from '@/lib/markdown';
import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'Hot Games',
  description: 'Discover the most popular and trending Pokemon games on our platform. Play the games everyone is talking about.',
};

export default async function HotGamesPage() {
  // Get hot games based on configuration
  const hotGames = await getHotGameFrontmatters();

  return (
    <div className="min-h-screen bg-white dark:bg-gray-900">
      {/* Header Section */}
      <section className="bg-gradient-to-r from-red-500 to-orange-600 text-white py-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-4">
            🔥 Hot Games
          </h1>
          <p className="text-xl text-red-100 max-w-2xl mx-auto">
            The most popular and trending games on our platform. Join millions of players!
          </p>
        </div>
      </section>

      {/* Games Grid Section */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          {hotGames.length > 0 ? (
            <>
              <div className="mb-8">
                <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                  Trending Now ({hotGames.length})
                </h2>
              </div>
              
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                {hotGames.map((game, index) => (
                  <div key={game.slug} className="relative">
                    {/* Trending Badge */}
                    {index < 3 && (
                      <div className="absolute top-2 right-2 z-10 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">
                        #{index + 1}
                      </div>
                    )}
                    <GameCard game={game} />
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-16">
              <div className="text-6xl mb-4">🔥</div>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                No Hot Games Available
              </h2>
              <p className="text-gray-600 dark:text-gray-300 mb-8">
                We&apos;re updating our hot games list. Check back soon!
              </p>
              <Link
                href="/new-games"
                className="bg-blue-600 hover:bg-blue-700 text-white font-semibold py-3 px-6 rounded-lg transition-colors inline-block"
              >
                Browse All Games
              </Link>
            </div>
          )}
        </div>
      </section>
    </div>
  );
} 