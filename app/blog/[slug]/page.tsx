import { getBlogBySlug, getBlogSlugs } from '@/lib/blog';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';
import Image from 'next/image';
import { generateBlogImageAlt } from '@/lib/alt-text-utils';

interface BlogPageProps {
  params: Promise<{
    slug: string;
  }>;
}

// Generate static params for all blogs
export async function generateStaticParams() {
  const slugs = getBlogSlugs();
  
  return slugs.map((slug) => ({
    slug: slug,
  }));
}

// Generate dynamic metadata for SEO
export async function generateMetadata({ params }: BlogPageProps): Promise<Metadata> {
  const resolvedParams = await params;
  const blogSlug = resolvedParams.slug;
  const blog = await getBlogBySlug(blogSlug);
  
  if (!blog) {
    return {
      title: 'Blog Post Not Found',
      description: 'The requested blog post could not be found.',
    };
  }

  return {
    title: blog.title,
    description: blog.description,
    authors: [{ name: blog.author }],
    openGraph: {
      title: blog.title,
      description: blog.description,
      type: 'article',
      url: `/blog/${blogSlug}`,
      images: [
        {
          url: blog.thumbnail,
          width: 1200,
          height: 630,
          alt: generateBlogImageAlt(blog.title),
        },
      ],
      siteName: 'pokemon-gamma-emerald.com',
      publishedTime: blog.publishedDate,
    },
    twitter: {
      card: 'summary_large_image',
      title: blog.title,
      description: blog.description,
      images: [blog.thumbnail],
    },
    alternates: {
      canonical: `/blog/${blogSlug}`,
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

export default async function BlogPage({ params }: BlogPageProps) {
  const resolvedParams = await params;
  const blogSlug = resolvedParams.slug;
  const blog = await getBlogBySlug(blogSlug);
  
  // If blog is not found, show 404 page
  if (!blog) {
    notFound();
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Blog Header */}
        <article className="bg-white dark:bg-gray-800 rounded-lg shadow-lg overflow-hidden">
          {/* Featured Image */}
          <div className="relative w-full h-64 md:h-80">
            <Image
              src={blog.thumbnail}
              alt={generateBlogImageAlt(blog.title)}
              fill
              className="object-cover"
              priority
            />
          </div>

          {/* Blog Content */}
          <div className="p-6 md:p-8">
            {/* Blog Meta */}
            <div className="mb-6">
              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
                <span>By {blog.author}</span>
                <span className="mx-2">•</span>
                <time dateTime={blog.publishedDate}>
                  {new Date(blog.publishedDate).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric',
                  })}
                </time>
              </div>
              
              <h1 className="text-3xl md:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                {blog.title}
              </h1>
              
              <p className="text-lg text-gray-600 dark:text-gray-300">
                {blog.description}
              </p>
            </div>

            {/* Blog Content */}
            <div 
              className="prose prose-lg dark:prose-invert max-w-none prose-headings:text-gray-900 dark:prose-headings:text-white prose-p:text-gray-700 dark:prose-p:text-gray-300 prose-a:text-blue-600 dark:prose-a:text-blue-400 prose-strong:text-gray-900 dark:prose-strong:text-white"
              dangerouslySetInnerHTML={{ __html: blog.contentHtml }}
            />
          </div>
        </article>
      </div>
    </div>
  );
} 