import { Metadata } from 'next';
import Link from 'next/link';

export const metadata: Metadata = {
  title: 'About Us',
  description: 'Learn about pokemon-gamma-emerald.com and our passion for sharing amazing Pokemon games with fans worldwide.',
};

export default function AboutUsPage() {
  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900 py-12">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
          <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-8 text-center">
            About Us
          </h1>
          
          <div className="prose prose-lg dark:prose-invert max-w-none">
            <div className="text-lg text-gray-700 dark:text-gray-300 leading-relaxed space-y-6">
              <p className="text-xl font-semibold text-blue-600 dark:text-blue-400">
                Hello, Pokémon fans!
              </p>
              
              <p>
                Welcome to pokemon-gamma-emerald.com! This is a special place for everyone who loves Pokémon games.
              </p>
              
              <p>
                I am a huge fan of Pokémon, just like you! I have loved playing Pokémon games since I was little. Pikachu, Charizard, and all the amazing Pokémon have been my friends for a long time.
                </p>
              
              <p>
                I made this website because I wanted to share cool Pokémon games with other fans. I hope you can find fun games here and enjoy playing them. It&apos;s all about having a good time with Pokémon!
              </p>
              
              <p className="text-lg font-medium text-green-600 dark:text-green-400">
                Thanks for visiting, and I hope you have lots of fun!
                </p>
              </div>
            
            <div className="mt-12 text-center">
              <Link href="/" className="inline-flex items-center justify-center space-x-4 bg-gradient-to-r from-blue-500 to-green-500 text-white px-8 py-4 rounded-lg hover:from-blue-600 hover:to-green-600 transition-all duration-200">
                <span className="text-2xl">⚡</span>
                <span className="font-semibold">Happy Gaming!</span>
                <span className="text-2xl">🎮</span>
              </Link>
            </div>
          </div>
          </div>
        </div>
    </div>
  );
} 