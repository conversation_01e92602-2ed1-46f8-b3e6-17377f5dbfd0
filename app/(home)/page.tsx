import GamePlayer from '@/components/GamePlayer';
import GameContentSection from '@/components/GameContentSection';
import RelatedGamesCarousel from '@/components/RelatedGamesCarousel';
import StaticMessageBoard from '@/components/StaticMessageBoard';
import StructuredData from '@/components/StructuredData';
import VerticalGameCards from '@/components/VerticalGameCards';
import { getGameBySlug, getRelatedGameFrontmatters, getAllGameFrontmatters, getHotGameFrontmatters } from '@/lib/markdown';
import { generateVideoGameSchema, generateWebSiteSchema, generateOrganizationSchema, combineStructuredData } from '@/lib/structured-data';
import { siteConfig } from '@/config/site';
import { Metadata } from 'next';
import { notFound } from 'next/navigation';

// 硬编码指定的核心游戏slug
const CORE_GAME_SLUG = 'pokemon-gamma-emerald';

// Generate dynamic metadata for SEO optimization
export async function generateMetadata(): Promise<Metadata> {
  const game = await getGameBySlug(CORE_GAME_SLUG);
  
  if (!game) {
    return {
      title: 'Play Free Pokemon Games',
      description: 'Discover and play amazing free online Pokemon games on our platform.',
    };
  }

  // Use the game's metadata for optimal SEO - keep title simple
  const pageTitle = game.title;
  const pageDescription = game.description;

  return {
    title: pageTitle,
    description: pageDescription,
    authors: game.author ? [{ name: game.author }] : [{ name: 'pokemon-gamma-emerald.com' }],
    openGraph: {
      title: game.title,
      description: pageDescription,
      type: 'website',
      url: '/',
      images: [
        {
          url: game.thumbnail,
          width: 1200,
          height: 630,
          alt: game.title,
        },
      ],
      siteName: 'pokemon-gamma-emerald.com',
    },
    twitter: {
      card: 'summary_large_image',
      title: game.title,
      description: pageDescription,
      images: [game.thumbnail],
    },
    alternates: {
      canonical: '/',
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
  };
}

// 生成首页结构化数据
function generateHomePageStructuredData(game: any) {
  const gameSchema = generateVideoGameSchema({
    title: game.title,
    slug: game.slug,
    description: game.description,
    thumbnail: game.thumbnail,
    genres: game.genres,
    publishedDate: game.publishedDate,
    author: game.author,
    version: game.version,
    iframeUrl: game.iframeUrl,
    pageDescription: game.pageDescription,
  }, siteConfig.url);

  const websiteSchema = generateWebSiteSchema();
  const organizationSchema = generateOrganizationSchema();

  return combineStructuredData(gameSchema, websiteSchema, organizationSchema);
}

export default async function HomePage() {
  // 获取核心游戏数据
  const game = await getGameBySlug(CORE_GAME_SLUG);
  
  // 如果核心游戏不存在，显示404
  if (!game) {
    notFound();
  }

  // 获取相关数据 - 调整逻辑
  const [relatedGames, newGames, hotGames] = await Promise.all([
    // Related Games: 获取Pokemon games分类的游戏（因为核心游戏是Pokemon类型）
    getAllGameFrontmatters({ filterByGenre: 'Pokemon games', sortByDate: true }).then(games => 
      games.filter(g => g.slug !== CORE_GAME_SLUG).slice(0, 8)
    ),
    // Latest Games: 获取最新游戏（与new-games页面一致）
    getAllGameFrontmatters({ sortByDate: true }).then(games => games.slice(0, 6)),
    // Hot Games: 获取热门游戏（与hot-games页面一致）
    getHotGameFrontmatters().then(games => games.slice(0, 6))
  ]);

  const structuredData = generateHomePageStructuredData(game);

  return (
    <>
      {/* 结构化数据 */}
      <StructuredData data={structuredData} />
      
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
        {/* 主内容区域 - 全宽布局，响应侧边栏状态 */}
        <div className="w-full px-2 sm:px-4 lg:px-6 py-4">
          {/* Main game area with responsive layout */}
          <div className="game-content-grid mb-6">
            {/* Left game cards - Hidden on mobile */}
            <div className="left-games hidden md:block">
              <VerticalGameCards
                games={newGames}
                title="Latest Games"
                maxItems={4}
              />
            </div>

            {/* Main game player - Expanded width */}
            <div className="main-player">
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 lg:p-8 transition-shadow duration-300 hover:shadow-xl">
                <GamePlayer
                  title={game.title}
                  displayTitle={game.displayTitle}
                  thumbnail={game.thumbnail}
                  iframeUrl={game.iframeUrl}
                  gameSlug={game.slug}
                  gameData={{
                    title: game.title,
                    slug: game.slug,
                    thumbnail: game.thumbnail,
                    genres: game.genres,
                    description: game.description,
                    iframeUrl: game.iframeUrl,
                    publishedDate: game.publishedDate,
                    author: game.author,
                    version: game.version,
                    pageDescription: game.pageDescription
                  }}
                  className="w-full"
                />
              </div>
            </div>

            {/* Right game cards - Hidden on mobile */}
            <div className="right-games hidden md:block">
              <VerticalGameCards
                games={hotGames}
                title="Hot Games"
                maxItems={4}
              />
            </div>
          </div>

          {/* Bottom carousel section - Related Games */}
          <div className="mb-6">
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 p-4 lg:p-8 transition-shadow duration-300 hover:shadow-xl">
              <RelatedGamesCarousel
                games={relatedGames}
                title="Related Games"
                autoPlay={true}
                autoPlayInterval={4000}
                cardSize="medium"
                showTags={true}
              />
            </div>
          </div>

          {/* 底部区域：左侧内容说明 + 右侧留言板 */}
          <div className="grid grid-cols-1 lg:grid-cols-12 gap-3 lg:gap-4">
            {/* 左侧游戏内容说明描述区 */}
            <div className="lg:col-span-8">
              <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                <GameContentSection 
                  game={game}
                  showGameMeta={true}
                  className="p-0" // 移除默认padding，因为容器已有
                  contentClassName="prose prose-sm lg:prose-lg prose-gray dark:prose-invert max-w-none"
                />
              </div>
            </div>

            {/* 右侧静态留言板 */}
            <div className="lg:col-span-4">
              <StaticMessageBoard 
                gameSlug={game.slug}
                className="h-fit"
                showNotice={true}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
