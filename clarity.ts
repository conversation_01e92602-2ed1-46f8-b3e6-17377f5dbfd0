// Microsoft Clarity tracking configuration and utilities

export const CLARITY_PROJECT_ID: string | null = process.env.NEXT_PUBLIC_CLARITY_ID || null;

// 追踪自定义事件
export const clarityEvent = (eventName: string, customData?: Record<string, any>): void => {
  if (typeof window !== 'undefined' && window.clarity) {
    if (customData) {
      window.clarity("event", eventName, customData);
    } else {
      window.clarity("event", eventName);
    }
  }
};

// 设置用户标识
export const clarityIdentify = (userId: string, sessionId?: string, pageId?: string): void => {
  if (typeof window !== 'undefined' && window.clarity) {
    window.clarity("identify", userId, sessionId, pageId);
  }
};

// 设置自定义标签
export const claritySet = (key: string, value: string): void => {
  if (typeof window !== 'undefined' && window.clarity) {
    window.clarity("set", key, value);
  }
};

// 同意数据收集
export const clarityConsent = (): void => {
  if (typeof window !== 'undefined' && window.clarity) {
    window.clarity("consent");
  }
};

// 扩展 Window 接口以包含 clarity
declare global {
  interface Window {
    clarity: (
      command: 'event' | 'identify' | 'set' | 'consent',
      ...args: any[]
    ) => void;
  }
} 